<?php
// 最高优先级：关闭所有错误输出到页面
ini_set('display_errors', 0);
error_reporting(0); // 完全禁用报告

// 在文件最开始，确保没有之前的输出
if (ob_get_level()) {
    ob_end_clean();
}

// 设置错误报告
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// 定义常量和配置
define('DEBUG_MODE', isset($_GET['debug']));
define('LOG_DIR', __DIR__ . '/logs');
define('LOG_CONFIG', [
    'api' => [
        'file' => LOG_DIR . '/api.log',
        'max_files' => 7,
        'format' => 'json'
    ],
    'debug' => [
        'file' => LOG_DIR . '/debug.log',
        'max_files' => 3,
        'format' => 'text'
    ],
    'security' => [
        'file' => LOG_DIR . '/security.log',
        'max_files' => 30,
        'format' => 'json'
    ],
    'error' => [
        'file' => LOG_DIR . '/error.log',
        'max_files' => 7,
        'format' => 'text'
    ]
]);

// 在文件开头添加命令行检测
$isCLI = (php_sapi_name() === 'cli');

// 修改原有的 REQUEST_METHOD 检查部分
if (!$isCLI) {
    // 处理 CORS
    if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
        // 预检请求处理
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
        header('Access-Control-Allow-Headers: Content-Type, Authorization, Cache-Control, Pragma, Expires, Accept');
        header('Access-Control-Max-Age: 1728000');
        header('Content-Type: text/plain');
        header('Content-Length: 0');
        exit(0);
    }
    // 非OPTIONS请求的CORS头部
    else {
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
        header('Content-Type: application/json; charset=utf-8');
    }
}

// 记录调试信息
if (DEBUG_MODE) {
    Logger::getInstance()->log('debug', [
        'headers_set' => headers_list(),
        'request_method' => $_SERVER['REQUEST_METHOD'],
        'request_uri' => $_SERVER['REQUEST_URI']
    ]);
}

// Cookie 设置（如果需要）
if (isset($value) && !empty($value)) {
    $cookie_options = array(
        'expires' => strtotime('9999-12-31 23:59:59'),
        'path' => '/',
        'domain' => 'api.geluman.cn',
        'secure' => true,
        'httponly' => true,
        'samesite' => 'Strict'
    );
    
    if (PHP_VERSION_ID >= 70300) {
        setcookie("X_CACHE_KEY", $value, $cookie_options);
    } else {
        setcookie(
            "X_CACHE_KEY",
            $value,
            $cookie_options['expires'],
            $cookie_options['path'] . '; secure; httponly; samesite=' . $cookie_options['samesite'],
            $cookie_options['domain'],
            true,
            true
        );
    }
}

// 确保日志目录存在
if (!file_exists(LOG_DIR)) {
    try {
        mkdir(LOG_DIR, 0755, true);
        // 记录目录创建成功
        error_log("Created log directory: " . LOG_DIR);
    } catch (Exception $e) {
        error_log("Failed to create log directory: " . $e->getMessage());
    }
}

// 检查目录权限
if (!is_writable(LOG_DIR)) {
    error_log("Log directory is not writable: " . LOG_DIR);
    // 尝试修改权限
    chmod(LOG_DIR, 0755);
}

// 添加日志工具类
class Logger {
    private static $instance = null;
    private $logConfig;
    private $loggingDisabled = false;
    
    private function __construct($config) {
        $this->logConfig = $config;
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self(LOG_CONFIG);
        }
        return self::$instance;
    }
    
    public function disableLogging() {
        $this->loggingDisabled = true;
    }
    
    public function log($type, $data) {
        if ($this->loggingDisabled) {
            return true;
        }
        
        // 确保命令行模式下也能正常记录日志
        if (defined('IS_CLI') && IS_CLI) {
            // 简化命令行下的日志记录
            if (is_array($data)) {
                // 移除可能导致命令行环境问题的键
                unset($data['headers_set']);
                unset($data['request_method']);
                unset($data['request_uri']);
            }
        }
        
        if (!isset($this->logConfig[$type])) {
            error_log("Invalid log type: " . $type);
            return false;
        }
        
        $config = $this->logConfig[$type];
        $logFile = $config['file'];
        
        try {
            // 确保日志文件所在目录存在
            $logDir = dirname($logFile);
            if (!file_exists($logDir)) {
                if (!@mkdir($logDir, 0755, true)) {
                    error_log("无法创建日志目录: " . $logDir);
                    return false;
                }
            }
            
            // 准备日志内容
            $logEntry = [
                'timestamp' => date('Y-m-d H:i:s'),
                'data' => $data
            ];
            
            if ($config['format'] === 'json') {
                $content = json_encode($logEntry) . "\n";
            } else {
                $content = "[{$logEntry['timestamp']}] " . 
                          (is_array($data) ? json_encode($data) : $data) . "\n";
            }
            
            // 使用错误抑制符写入日志，避免权限错误被显示给用户
            $result = @file_put_contents($logFile, $content, FILE_APPEND);
            
            // 如果写入失败但不是因为权限问题，尝试写入到备用位置
            if ($result === false) {
                $fallbackLog = sys_get_temp_dir() . '/api_fallback.log';
                @file_put_contents($fallbackLog, "无法写入原始日志: {$logFile}, 内容: " . substr($content, 0, 200) . "\n", FILE_APPEND);
                return false;
            }
            
            return true;
        } catch (Exception $e) {
            error_log("日志错误: " . $e->getMessage());
            return false;
        }
    }
    
    private function rotateLogFile($config) {
        $baseFile = $config['file'];
        $maxFiles = $config['max_files'];
        $result = ['rotated' => false, 'size' => 0];
        
        if (file_exists($baseFile)) {
            $fileSize = filesize($baseFile);
            $result['size'] = $fileSize;
            
            // 检查文件大小是否超过3MB (3 * 1024 * 1024 字节)
            if ($fileSize > 3 * 1024 * 1024) {
                // 移动旧文件
                for ($i = $maxFiles - 1; $i > 0; $i--) {
                    $oldFile = $baseFile . '.' . $i;
                    $newFile = $baseFile . '.' . ($i + 1);
                    if (file_exists($oldFile)) {
                        rename($oldFile, $newFile);
                    }
                }
                
                // 移动当前文件
                if (file_exists($baseFile)) {
                    if (rename($baseFile, $baseFile . '.1')) {
                        // 创建新的空文件
                        touch($baseFile);
                        chmod($baseFile, 0644);
                        $result['rotated'] = true;
                    }
                }
            }
        }
        
        return $result;
    }

    public function cleanOldLogs() {
        $results = [];
        
        foreach ($this->logConfig as $type => $config) {
            $baseFile = $config['file'];
            $maxFiles = $config['max_files'];
            $results[$type] = ['removed' => 0];
            
            // 清理超过最大数量的日志文件
            for ($i = $maxFiles + 1; ; $i++) {
                $oldFile = $baseFile . '.' . $i;
                if (file_exists($oldFile)) {
                    if (unlink($oldFile)) {
                        $results[$type]['removed']++;
                    }
                } else {
                    break;
                }
            }
        }
        
        return $results;
    }

    public function rotateAndCleanLogs() {
        $results = [];
        
        // 首先检查并轮转每种类型的日志文件
        foreach ($this->logConfig as $type => $config) {
            $results[$type] = $this->rotateLogFile($config);
        }
        
        // 然后清理旧日志
        $this->cleanOldLogs();
        
        return $results;
    }

    // 在Logger类中添加自动轮转检查
    public function checkAndRotateIfNeeded() {
        foreach ($this->logConfig as $type => $config) {
            $baseFile = $config['file'];
            if (file_exists($baseFile) && filesize($baseFile) > 3 * 1024 * 1024) {
                $this->rotateLogFile($config);
            }
        }
    }
}

// 错误处理函数
function handleError($message, $code = 500) {
    http_response_code($code);
    echo json_encode([
        'error' => $message,
        'timestamp' => date('Y-m-d H:i:s'),
        'debug' => DEBUG_MODE ? [
            'request_uri' => $_SERVER['REQUEST_URI'],
            'request_method' => $_SERVER['REQUEST_METHOD'],
            'headers' => getallheaders()
        ] : null
    ]);
    exit();
}

// 配置文件路径
$modelConfigFile = __DIR__ . '/models.json';

// GET请求处理
if (!$isCLI && $_SERVER['REQUEST_METHOD'] === 'GET') {
    // 确保没有输出缓冲
    while (ob_get_level() > 0) {
        ob_end_clean();
    }
    
    // 设置通用响应头
    header('Content-Type: application/json; charset=utf-8');
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
    // 添加缓存控制头
    header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
    header('Cache-Control: post-check=0, pre-check=0', false);
    header('Pragma: no-cache');
    header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');
    
    // 处理配置请求 - 极简方案
    if (isset($_GET['config']) && $_GET['config'] === 'models') {
        // 禁用错误输出
        ini_set('display_errors', 0);
        error_reporting(0);
        
        // 确保没有之前的输出
        if (ob_get_level() > 0) {
            while (ob_get_level() > 0) {
                ob_end_clean();
            }
        }
        
        // 设置头部
        header('Content-Type: application/json; charset=utf-8');
        header('Access-Control-Allow-Origin: *');
        header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
        
        // 读取并输出配置
        if (file_exists(__DIR__ . '/models.json')) {
            $config = json_decode(file_get_contents(__DIR__ . '/models.json'), true);
            $response = [
                'status' => 'success',
                'config' => $config,
                'timestamp' => date('Y-m-d H:i:s')
            ];
            echo json_encode($response);
        } else {
            echo json_encode([
                'status' => 'error',
                'message' => '配置文件不存在'
            ]);
        }
        exit();
    }
    // 处理调试请求
    else if (isset($_GET['debug'])) {
        $debugInfo = [
            'server' => [
                'software' => $_SERVER['SERVER_SOFTWARE'],
                'php_version' => PHP_VERSION,
                'time' => date('Y-m-d H:i:s'),
                'request_method' => $_SERVER['REQUEST_METHOD'],
                'request_uri' => $_SERVER['REQUEST_URI']
            ],
            'headers' => [
                'request' => getallheaders(),
                'response' => headers_list()
            ],
            'files' => [
                'models_json_exists' => file_exists($modelConfigFile),
                'models_json_readable' => is_readable($modelConfigFile),
                'models_json_content' => file_exists($modelConfigFile) ? json_decode(file_get_contents($modelConfigFile), true) : null
            ]
        ];
        
        $jsonResponse = json_encode($debugInfo, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        header('Content-Length: ' . strlen($jsonResponse));
        echo $jsonResponse;
        exit();
    }
    // 处理默认请求
    else if (isset($_GET['default'])) {
        // 返回一个基础的默认配置，不包含API密钥等敏感信息
        $defaultConfig = [
            'status' => 'success',
            'config' => [
                'translation' => [
                    'default_model' => 'default_model', // 服务器默认模型
                    'options' => [
                        // 返回的是模型列表，但不包含API密钥
                    ]
                ],
                'abbreviation' => [
                    'default_model' => 'default_model',
                    'options' => []
                ]
            ],
            'message' => '使用服务器默认配置',
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        echo json_encode($defaultConfig);
        exit();
    }
    else {
        $availableModels = [];
        
        if (file_exists($modelConfigFile)) {
            $modelConfigs = json_decode(file_get_contents($modelConfigFile), true);
            if (isset($modelConfigs['translation']['options'])) {
                foreach ($modelConfigs['translation']['options'] as $modelId => $modelData) {
                    $availableModels[] = $modelId;
                }
            }
            if (isset($modelConfigs['abbreviation']['options'])) {
                foreach ($modelConfigs['abbreviation']['options'] as $modelId => $modelData) {
                    if (!in_array($modelId, $availableModels)) {
                        $availableModels[] = $modelId;
                    }
                }
            }
        }
        
        $response = [
            'status' => 'ready',
            'message' => 'API代理服务器已准备就绪，请使用POST方法发送请求',
            'available_models' => $availableModels,
            'server_time' => date('Y-m-d H:i:s')
        ];
        
        $jsonResponse = json_encode($response, JSON_UNESCAPED_UNICODE);
        header('Content-Length: ' . strlen($jsonResponse));
        echo $jsonResponse;
        exit();
    }
}

// POST请求处理
if (!$isCLI && $_SERVER['REQUEST_METHOD'] === 'POST') {
    // 读取请求体
    $requestBody = file_get_contents('php://input');
    $requestData = json_decode($requestBody, true) ?: [];
    
    // 检查是否为流式请求
    $isStreamRequest = isset($requestData['stream']) && $requestData['stream'] === true;
    
    // 获取请求类型 - 首先检查是否明确指定了request_type
    $requestType = isset($requestData['request_type']) ? $requestData['request_type'] : 'translation';
    
    // 如果没有明确指定request_type，尝试从消息内容判断
    if ($requestType === 'translation' && isset($requestData['messages']) && is_array($requestData['messages'])) {
        foreach ($requestData['messages'] as $message) {
            if (isset($message['role']) && $message['role'] === 'user' && isset($message['content'])) {
                $content = $message['content'];
                // 检查内容是否包含解读相关关键词
                if (strpos($content, '解读') !== false || 
                    strpos($content, '含义') !== false || 
                    strpos($content, '缩写') !== false || 
                    strpos($content, '网络用语') !== false) {
                    $requestType = 'abbreviation';
                    Logger::getInstance()->log('debug', [
                        'event' => 'auto_detected_abbreviation_request',
                        'content' => substr($content, 0, 100) . (strlen($content) > 100 ? '...' : '')
                    ]);
                    break;
                }
            }
        }
    }
    
    // 记录请求类型
    Logger::getInstance()->log('debug', [
        'event' => 'request_type_detected',
        'request_type' => $requestType,
        'request_data' => $requestData
    ]);
    
    // 加载模型配置
    $modelConfigFile = __DIR__ . '/models.json';
    $modelConfigs = [];
    if (file_exists($modelConfigFile)) {
        $modelConfigs = json_decode(file_get_contents($modelConfigFile), true);
        
        // 记录加载的模型配置
        Logger::getInstance()->log('debug', [
            'event' => 'loaded_model_configs',
            'config_file' => $modelConfigFile,
            'models_found' => $modelConfigs
        ]);
    }
    
    // 根据请求类型选择默认模型
    $defaultModel = null;
    if ($requestType === 'abbreviation') {
        // 使用解读模型
        $defaultModel = $modelConfigs['abbreviation']['default_model'] ?? null;
        
        // 记录选择的模型
        Logger::getInstance()->log('debug', [
            'event' => 'selected_abbreviation_model',
            'model' => $defaultModel
        ]);
    } else {
        // 使用翻译模型
        $defaultModel = $modelConfigs['translation']['default_model'] ?? null;
        
        // 记录选择的模型
        Logger::getInstance()->log('debug', [
            'event' => 'selected_translation_model',
            'model' => $defaultModel
        ]);
    }
    
    // 如果请求中没有指定模型，使用默认模型
    if (!isset($requestData['model']) && $defaultModel) {
        $requestData['model'] = $defaultModel;
        
        // 记录使用默认模型
        Logger::getInstance()->log('debug', [
            'event' => 'using_default_model',
            'model' => $defaultModel,
            'request_type' => $requestType
        ]);
    }
    
    // 获取最终使用的模型
    $model = $requestData['model'] ?? $defaultModel;
    
    // 记录最终使用的模型
    Logger::getInstance()->log('debug', [
        'event' => 'final_model_selection',
        'model' => $model,
        'request_type' => $requestType
    ]);
    
    // 根据模型选择API URL
    $apiUrl = null;
    
    // 查找模型配置
    Logger::getInstance()->log('debug', [
        'event' => 'finding_model_config',
        'model' => $model,
        'available_models' => $modelConfigs
    ]);
    
    // 根据请求类型和模型名称查找API URL
    if ($requestType === 'abbreviation') {
        if (isset($modelConfigs['abbreviation']['options'][$model])) {
            $apiUrl = $modelConfigs['abbreviation']['options'][$model]['api_url'] ?? null;
        }
    } else {
        if (isset($modelConfigs['translation']['options'][$model])) {
            $apiUrl = $modelConfigs['translation']['options'][$model]['api_url'] ?? null;
        }
    }
    
    // 如果没有找到API URL，使用默认模型的API URL
    if (!$apiUrl) {
        if ($requestType === 'abbreviation') {
            $defaultModel = $modelConfigs['abbreviation']['default_model'] ?? null;
            if ($defaultModel && isset($modelConfigs['abbreviation']['options'][$defaultModel])) {
                $apiUrl = $modelConfigs['abbreviation']['options'][$defaultModel]['api_url'] ?? null;
                $model = $defaultModel; // 使用默认模型
            }
        } else {
            $defaultModel = $modelConfigs['translation']['default_model'] ?? null;
            if ($defaultModel && isset($modelConfigs['translation']['options'][$defaultModel])) {
                $apiUrl = $modelConfigs['translation']['options'][$defaultModel]['api_url'] ?? null;
                $model = $defaultModel; // 使用默认模型
            }
        }
    }
    
    // 加载API密钥
    $apiKeyFile = __DIR__ . '/api_keys.json';
    $apiKeys = [];
    if (file_exists($apiKeyFile)) {
        $apiKeys = json_decode(file_get_contents($apiKeyFile), true);
    }
    
    // 获取API密钥
    $apiKey = $apiKeys[$model] ?? $apiKeys['default'] ?? null;
    
    // 记录API URL和密钥状态
    Logger::getInstance()->log('debug', [
        'event' => 'api_config',
        'model' => $model,
        'api_url' => $apiUrl,
        'api_key_exists' => !empty($apiKey)
    ]);
    
    // 在转发请求到实际API之前，确保请求体不为空
    if (empty($requestBody)) {
        http_response_code(400);
        echo json_encode(['error' => '请求体为空']);
        exit();
    }
    
    if (empty($apiUrl)) {
        http_response_code(400);
        echo json_encode(['error' => 'API URL为空']);
        exit();
    }
    
    if (empty($apiKey)) {
        http_response_code(400);
        echo json_encode(['error' => 'API密钥为空']);
        exit();
    }
    
    // 根据不同API提供商调整认证头格式
    if (strpos($apiUrl, 'ark.cn-beijing.volces.com') !== false) {
        // 火山引擎API需要在API密钥前添加"Bearer "前缀
        $authHeader = 'Authorization: Bearer ' . $apiKey;
    } elseif (strpos($apiUrl, 'siliconflow.cn') !== false) {
        // 智谱AI可能有特殊格式
        $authHeader = 'Authorization: Bearer ' . $apiKey;
    } else {
        // 标准OpenAI兼容API
        $authHeader = 'Authorization: Bearer ' . $apiKey;
    }
    
    // 确保请求体包含model字段
    $requestData['model'] = $model;
    
    // 如果是流式请求，添加stream=true
    if ($isStreamRequest) {
        $requestData['stream'] = true;
    }
    
    $requestBody = json_encode($requestData);
    
    // 记录将要发送的数据
    Logger::getInstance()->log('debug', [
        'event' => 'sending_request',
        'api_url' => $apiUrl,
        'model' => $model,
        'is_stream' => $isStreamRequest,
        'request_body' => substr($requestBody, 0, 200) . (strlen($requestBody) > 200 ? '...' : '')
    ]);
    
    // 初始化cURL会话
    $ch = curl_init($apiUrl);
    
    // 设置请求选项，根据不同API调整认证头
    $curlOptions = [
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => $requestBody,
        CURLOPT_HTTPHEADER => [
            'Content-Type: application/json',
            $authHeader
        ],
        CURLOPT_TIMEOUT => 120, // 增加超时时间，流式响应可能需要更长时间
        CURLOPT_CONNECTTIMEOUT => 10,
        CURLOPT_SSL_VERIFYPEER => false
    ];
    
    // 对于流式请求采用不同的处理方式
    if ($isStreamRequest) {
        // 关闭输出缓冲
        if (ob_get_level()) ob_end_clean();
        
        // 设置正确的响应头
        header('Content-Type: text/event-stream');
        header('Cache-Control: no-cache');
        header('Connection: keep-alive');
        header('X-Accel-Buffering: no'); // 禁用nginx的缓冲
        
        // 使用回调函数处理流式响应
        $curlOptions[CURLOPT_RETURNTRANSFER] = false;
        $curlOptions[CURLOPT_WRITEFUNCTION] = function($curl, $data) {
            echo $data;
            flush();
            return strlen($data);
        };
    } else {
        // 非流式请求，正常处理
        $curlOptions[CURLOPT_RETURNTRANSFER] = true;
    }
    
    curl_setopt_array($ch, $curlOptions);
    
    try {
        // 对于流式请求，直接执行curl，数据会通过回调函数输出
        if ($isStreamRequest) {
            $success = curl_exec($ch);
            $error = curl_error($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            
            // 记录响应状态
            Logger::getInstance()->log('debug', [
                'event' => 'stream_response_complete',
                'http_code' => $httpCode,
                'error' => $error,
                'success' => $success
            ]);
            
            if (!$success) {
                // 输出错误信息，保持SSE格式
                echo "data: " . json_encode(['error' => $error]) . "\n\n";
            }
            
            // 确保最后发送[DONE]信号
            echo "data: [DONE]\n\n";
            
        } else {
            // 非流式请求，按原来的方式处理
            $response = curl_exec($ch);
            $error = curl_error($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            
            // 记录响应
            Logger::getInstance()->log('debug', [
                'event' => 'api_response',
                'http_code' => $httpCode,
                'error' => $error,
                'response_length' => strlen($response),
                'response_preview' => substr($response, 0, 200)
            ]);
            
            if ($error) {
                throw new Exception('cURL Error: ' . $error);
            }
            
            // 返回API响应
            http_response_code($httpCode);
            header('Content-Type: application/json');
            echo $response;
        }
        
        // 定义客户端ID（使用IP地址或其他标识）
        $clientId = $_SERVER['REMOTE_ADDR'];
        
        // 记录使用情况
        recordUsage($clientId, $model);
        
        // 添加这一行来记录API请求
        logRequest($model, $requestData, $httpCode, $clientId);
        
    } catch (Exception $e) {
        if ($isStreamRequest) {
            // 流式响应的错误处理
            echo "data: " . json_encode(['error' => $e->getMessage()]) . "\n\n";
            echo "data: [DONE]\n\n";
        } else {
            // 正常响应的错误处理
            http_response_code(500);
            echo json_encode([
                'error' => $e->getMessage(),
                'timestamp' => date('Y-m-d H:i:s')
            ]);
        }
    }
    
    curl_close($ch);
    exit();
}

// 临时修改validateRequest函数用于测试
function validateRequest() {
    // 临时返回true以排除验证问题
    return true;
    
}

// 新增函数：记录安全事件
function logSecurityEvent($eventType, $ip, $details) {
    $logEntry = [
        'timestamp' => date('Y-m-d H:i:s'),
        'event_type' => $eventType,
        'ip' => $ip,
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
        'request_uri' => $_SERVER['REQUEST_URI'],
        'details' => $details
    ];
    
    Logger::getInstance()->log('security', $logEntry);
}

// 基于客户端ID的速率限制（IP或会话ID）
function shouldRateLimit($clientId) {
    $rateLimitFile = LOG_DIR . '/rate_limits.json';
    $currentTime = time();
    $limits = [];
    
    // 确保日志目录存在
    if (!file_exists(LOG_DIR)) {
        mkdir(LOG_DIR, 0755, true);
    }
    
    // 如果文件不存在，创建一个新的空文件
    if (!file_exists($rateLimitFile)) {
        file_put_contents($rateLimitFile, json_encode([]));
        chmod($rateLimitFile, 0666); // 设置权限
    }
    
    // 读取限制记录
    if (file_exists($rateLimitFile)) {
        $content = file_get_contents($rateLimitFile);
        if ($content) {
            $limits = json_decode($content, true) ?: [];
        }
    }
    
    // 清理过期记录 - 保留最近一小时的记录
    foreach ($limits as $id => $records) {
        $limits[$id] = array_filter($records, function($timestamp) use ($currentTime) {
            return $currentTime - $timestamp < 3600;
        });
    }
    
    // 多层次限制检查
    if (isset($limits[$clientId])) {
        // 检查最近10秒内的请求次数 (短期限制)
        $recentRequests = array_filter($limits[$clientId], function($timestamp) use ($currentTime) {
            return $currentTime - $timestamp < 10;
        });
        
        // 检查最近60秒内的请求次数 (中期限制)
        $minuteRequests = array_filter($limits[$clientId], function($timestamp) use ($currentTime) {
            return $currentTime - $timestamp < 60;
        });
        
        // 检查最近一小时的请求次数 (长期限制)
        $hourRequests = $limits[$clientId];
        
        // 应用分层速率限制
        if (count($recentRequests) >= 5) {
            // 10秒内最多5次请求
            return true;
        }
        
        if (count($minuteRequests) >= 20) {
            // 1分钟内最多20次请求
            return true;
        }
        
        if (count($hourRequests) >= 200) {
            // 1小时内最多200次请求
            return true;
        }
    }
    
    return false;
}

// 记录使用情况
function recordUsage($clientId, $model) {
    $rateLimitFile = LOG_DIR . '/rate_limits.json';
    $limits = [];
    
    // 确保日志目录存在
    if (!file_exists(LOG_DIR)) {
        mkdir(LOG_DIR, 0755, true);
    }
    
    // 如果文件不存在，创建一个新的空文件
    if (!file_exists($rateLimitFile)) {
        file_put_contents($rateLimitFile, json_encode([]));
        chmod($rateLimitFile, 0666); // 设置权限
    }
    
    // 读取现有数据
    if (file_exists($rateLimitFile)) {
        $content = file_get_contents($rateLimitFile);
        if ($content) {
            $limits = json_decode($content, true) ?: [];
        }
    }
    
    // 添加新的使用记录
    if (!isset($limits[$clientId])) {
        $limits[$clientId] = [];
    }
    
    $limits[$clientId][] = time();
    
    // 保存更新后的限制记录
    $result = file_put_contents($rateLimitFile, json_encode($limits));
    
    // 确保文件权限正确
    chmod($rateLimitFile, 0666);
    
    // 记录调试信息
    Logger::getInstance()->log('debug', [
        'event' => 'rate_limit_record',
        'client_id' => $clientId,
        'model' => $model,
        'file_path' => $rateLimitFile,
        'write_result' => $result !== false,
        'file_exists' => file_exists($rateLimitFile),
        'file_permissions' => substr(sprintf('%o', fileperms($rateLimitFile)), -4)
    ]);
}

// 记录请求日志
function logRequest($model, $data, $httpCode, $clientId) {
    global $isCLI;
    $logEntry = [
        'timestamp' => date('Y-m-d H:i:s'),
        'model' => $model,
        'status' => $httpCode,
        'client_id' => $clientId,
        'endpoint' => $isCLI ? 'CLI' : $_SERVER['REQUEST_URI'],
        'method' => $isCLI ? 'CLI' : $_SERVER['REQUEST_METHOD'],
        'ip' => $isCLI ? 'localhost' : $_SERVER['REMOTE_ADDR'],
        'user_agent' => $isCLI ? 'PHP CLI' : ($_SERVER['HTTP_USER_AGENT'] ?? 'unknown'),
        'request_data' => $data
    ];
    
    Logger::getInstance()->log('api', $logEntry);
}

// 在GET请求处理开始时添加调试日志
if (!$isCLI && $_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['config']) && $_GET['config'] === 'models') {
    Logger::getInstance()->log('debug', [
        'event' => 'loading_config',
        'file_exists' => file_exists($modelConfigFile),
        'file_content' => file_exists($modelConfigFile) ? file_get_contents($modelConfigFile) : null,
        'request_uri' => $_SERVER['REQUEST_URI']
    ]);
}

// 添加配置接口端点
if (isset($_GET['config']) && $_GET['config'] === 'models') {
    // 读取模型配置文件
    $modelConfigFile = __DIR__ . '/models.json';
    
    if (file_exists($modelConfigFile)) {
        $modelConfigs = json_decode(file_get_contents($modelConfigFile), true);
        
        if (json_last_error() === JSON_ERROR_NONE) {
            // 返回成功响应
            echo json_encode([
                'status' => 'success',
                'config' => $modelConfigs
            ]);
        } else {
            // JSON解析错误
            http_response_code(500);
            echo json_encode([
                'status' => 'error',
                'message' => 'JSON解析错误: ' . json_last_error_msg()
            ]);
        }
    } else {
        // 配置文件不存在
        http_response_code(404);
        echo json_encode([
            'status' => 'error',
            'message' => '配置文件不存在'
        ]);
    }
    exit();
}

// 添加调试端点
if (isset($_GET['debug']) && $_GET['debug'] === 'config') {
    // 返回详细的配置信息
    $modelConfigFile = __DIR__ . '/models.json';
    $response = [
        'file_exists' => file_exists($modelConfigFile),
        'file_size' => file_exists($modelConfigFile) ? filesize($modelConfigFile) : 0,
        'file_permissions' => file_exists($modelConfigFile) ? substr(sprintf('%o', fileperms($modelConfigFile)), -4) : 'N/A',
        'file_content' => file_exists($modelConfigFile) ? file_get_contents($modelConfigFile) : null,
        'json_valid' => false,
        'parsed_data' => null
    ];
    
    if (file_exists($modelConfigFile)) {
        $content = file_get_contents($modelConfigFile);
        $parsed = json_decode($content, true);
        $response['json_valid'] = (json_last_error() === JSON_ERROR_NONE);
        $response['json_error'] = json_last_error_msg();
        $response['parsed_data'] = $parsed;
    }
    
    header('Content-Type: application/json');
    echo json_encode($response, JSON_PRETTY_PRINT);
    exit();
}
?> 