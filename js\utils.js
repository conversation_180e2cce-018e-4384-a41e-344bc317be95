// 通用工具函数
window.Utils = {
  // 错误类型定义
  ErrorTypes: {
    VALIDATION: "validation_error",
    RUNTIME: "runtime_error",
    NETWORK: "network_error",
    STORAGE: "storage_error",
    DOM: "dom_error",
  },

  // 统一的错误处理
  log(message, level = "INFO", context = "") {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] [${level}] ${
      context ? context + ": " : ""
    }${message}`;

    switch (level) {
      case "ERROR":
        console.error(logMessage);
        break;
      case "WARN":
        console.warn(logMessage);
        break;
      case "INFO":
        console.info(logMessage);
        break;
      case "DEBUG":
        console.debug(logMessage);
        break;
      default:
        console.log(logMessage);
    }
  },

  handleError(error, context = "", type = "RUNTIME") {
    this.log(error.message, "ERROR", `${type} ${context}`);
    // 可以添加错误上报等功能
  },

  // 优化的防抖函数
  debounce(func, wait, options = {}) {
    let timeout;
    let lastArgs;
    let lastThis;
    let result;

    const leading = options.leading === true;
    const trailing = options.trailing !== false;

    return function (...args) {
      lastArgs = args;
      lastThis = this;

      if (!timeout && leading) {
        result = func.apply(lastThis, lastArgs);
      }

      clearTimeout(timeout);

      timeout = setTimeout(() => {
        if (trailing && lastArgs) {
          result = func.apply(lastThis, lastArgs);
        }
        timeout = null;
        lastArgs = null;
        lastThis = null;
      }, wait);

      return result;
    };
  },

  // 优化的节流函数
  throttle(func, limit, options = {}) {
    let inThrottle;
    let lastResult;
    let lastTime = 0;

    const leading = options.leading !== false;
    const trailing = options.trailing !== false;

    return function (...args) {
      const now = Date.now();

      if (!inThrottle && leading) {
        lastResult = func.apply(this, args);
        lastTime = now;
        inThrottle = true;
      }

      if (now - lastTime >= limit) {
        lastResult = func.apply(this, args);
        lastTime = now;
        inThrottle = false;
      } else if (trailing) {
        clearTimeout(inThrottle);
        inThrottle = setTimeout(() => {
          if (Date.now() - lastTime >= limit) {
            lastResult = func.apply(this, args);
            lastTime = Date.now();
          }
        }, limit - (now - lastTime));
      }

      return lastResult;
    };
  },

  // 消息验证
  verifyMessage(message, type = "rpc") {
    if (!message || typeof message !== "object") {
      return this.handleError(
        new Error("Invalid message format"),
        "verifyMessage",
        "VALIDATION"
      );
    }

    // 处理添加到分类的消息
    if (message.type === "add-to-category") {
      return { success: true };
    }

    // 处理其他类型消息
    switch (type) {
      case "rpc":
        if (!message.opt || !message.func) {
          return this.handleError(
            new Error("Invalid RPC message"),
            "verifyMessage",
            "VALIDATION"
          );
        }
        break;
      case "event":
        if (!message.opt || !message.event) {
          return this.handleError(
            new Error("Invalid event message"),
            "verifyMessage",
            "VALIDATION"
          );
        }
        break;
    }

    return { success: true };
  },

  // 安全的DOM操作
  safeDOM: {
    getElement(selector, context = document) {
      try {
        return context.querySelector(selector);
      } catch (error) {
        return Utils.handleError(error, "getElement", "DOM").error;
      }
    },

    addEvent(element, event, handler, options = {}) {
      try {
        if (element && typeof handler === "function") {
          element.addEventListener(event, handler, options);
          return true;
        }
        return false;
      } catch (error) {
        return Utils.handleError(error, "addEvent", "DOM").error;
      }
    },

    removeEvent(element, event, handler, options = {}) {
      try {
        if (element && typeof handler === "function") {
          element.removeEventListener(event, handler, options);
          return true;
        }
        return false;
      } catch (error) {
        return Utils.handleError(error, "removeEvent", "DOM").error;
      }
    },
  },

  // 异步工具
  async: {
    retry(fn, options = {}) {
      const { retries = 3, delay = 1000 } = options;
      let attempt = 0;

      const execute = async () => {
        try {
          return await fn();
        } catch (error) {
          if (++attempt >= retries) throw error;
          await new Promise((resolve) => setTimeout(resolve, delay));
          return execute();
        }
      };

      return execute();
    },
  },

  // 添加 DOM 相关工具方法
  dom: {
    // 优化节点基础检查，避免频繁的 DOM 树遍历
    isValidNode(node) {
      return (
        node &&
        node.parentNode &&
        // 只在必要时检查 DOM 树
        (node.ownerDocument === document || document.contains(node))
      );
    },

    // 检查是否为文本节点
    isTextNode(node) {
      return node?.nodeType === Node.TEXT_NODE;
    },

    // 统一的节点跳过检查
    shouldSkipNode(node, config) {
      if (!node || !node.parentNode) return true;
      const parent = node.parentElement || node.parentNode;
      return (
        !parent ||
        parent.classList?.contains(config.className) ||
        config.filterRules.shouldSkipTag(parent) ||
        !config.filterRules.shouldAllowInput(parent) ||
        config.filterRules.isEditable(parent)
      );
    },

    // 添加更多常用的 DOM 操作方法
    safeRemove(node) {
      if (node && node.parentNode) {
        node.parentNode.removeChild(node);
      }
    },
  },

  // 性能优化工具 - 使用统一的防抖节流实现
  performance: {
    // 使用主要的防抖函数实现
    debounce: (fn, wait, options = {}) => Utils.debounce(fn, wait, options),
    
    // 使用主要的节流函数实现
    throttle: (fn, limit, options = {}) => Utils.throttle(fn, limit, options),
  },

  // 统一缓存管理系统
  cache: {
    // 增强的LRU缓存实现
    LRUCache: class {
      constructor(limit = 1000, ttl = null) {
        this.limit = limit;
        this.ttl = ttl; // 可选的TTL支持
        this.cache = new Map();
        this.accessOrder = [];
      }

      get(key) {
        const item = this.cache.get(key);
        if (!item) return undefined;

        // 检查TTL
        if (this.ttl && Date.now() - item.timestamp > this.ttl) {
          this.delete(key);
          return undefined;
        }

        // 更新访问顺序
        this._updateAccess(key);
        return item.value;
      }

      set(key, value) {
        const now = Date.now();
        
        if (this.cache.has(key)) {
          // 更新现有项
          this.cache.set(key, { value, timestamp: now });
          this._updateAccess(key);
        } else {
          // 添加新项
          if (this.cache.size >= this.limit) {
            // 删除最少使用的项
            const lruKey = this.accessOrder.shift();
            this.cache.delete(lruKey);
          }
          
          this.cache.set(key, { value, timestamp: now });
          this.accessOrder.push(key);
        }
      }

      delete(key) {
        if (this.cache.has(key)) {
          this.cache.delete(key);
          const index = this.accessOrder.indexOf(key);
          if (index > -1) {
            this.accessOrder.splice(index, 1);
          }
          return true;
        }
        return false;
      }

      clear() {
        this.cache.clear();
        this.accessOrder = [];
      }

      size() {
        return this.cache.size;
      }

      // 获取缓存统计信息
      getStats() {
        return {
          size: this.cache.size,
          limit: this.limit,
          usage: (this.cache.size / this.limit * 100).toFixed(2) + '%',
          ttl: this.ttl
        };
      }

      _updateAccess(key) {
        const index = this.accessOrder.indexOf(key);
        if (index > -1) {
          this.accessOrder.splice(index, 1);
        }
        this.accessOrder.push(key);
      }
    },

    // 缓存管理器工厂
    createManager(type, options = {}) {
      switch (type) {
        case 'pattern':
          return new this.LRUCache(options.maxSize || 500, options.ttl || 300000); // 5分钟TTL
        case 'dom':
          return new this.LRUCache(options.maxSize || 800, options.ttl || 60000);  // 1分钟TTL
        case 'keyword':
          return new this.LRUCache(options.maxSize || 100, options.ttl || 600000); // 10分钟TTL
        default:
          return new this.LRUCache(options.maxSize || 1000, options.ttl);
      }
    }
  },

  // 消息批处理机制 - 优化通信开销
  messaging: {
    _queue: [],
    _timer: null,
    _batchSize: 10,
    _batchDelay: 50, // 50ms批处理延迟

    // 添加消息到批处理队列
    addToBatch(message, priority = 'normal') {
      const batchMessage = {
        ...message,
        _timestamp: Date.now(),
        _priority: priority
      };

      // 高优先级消息插入队列前部
      if (priority === 'high') {
        this._queue.unshift(batchMessage);
      } else {
        this._queue.push(batchMessage);
      }

      // 如果队列满了或有高优先级消息，立即处理
      if (this._queue.length >= this._batchSize || priority === 'high') {
        this._flushBatch();
      } else {
        // 否则延迟处理
        this._scheduleBatch();
      }
    },

    // 调度批处理
    _scheduleBatch() {
      if (this._timer) return;
      
      this._timer = setTimeout(() => {
        this._flushBatch();
      }, this._batchDelay);
    },

    // 执行批处理
    _flushBatch() {
      if (this._timer) {
        clearTimeout(this._timer);
        this._timer = null;
      }

      if (this._queue.length === 0) return;

      const messages = this._queue.splice(0);
      
      // 按优先级排序
      messages.sort((a, b) => {
        const priorityOrder = { high: 0, normal: 1, low: 2 };
        return priorityOrder[a._priority] - priorityOrder[b._priority];
      });

      // 发送批处理消息
      if (typeof chrome !== 'undefined' && chrome.runtime) {
        chrome.runtime.sendMessage({
          opt: 'batch',
          messages: messages,
          timestamp: Date.now()
        }).catch(error => {
          Utils.handleError(error, 'messageBatch', 'NETWORK');
          // 重要消息重新入队
          const importantMessages = messages.filter(msg => msg._priority === 'high');
          this._queue.unshift(...importantMessages);
        });
      }
    },

    // 立即发送消息（绕过批处理）
    sendImmediate(message) {
      if (typeof chrome !== 'undefined' && chrome.runtime) {
        return chrome.runtime.sendMessage(message).catch(error => {
          Utils.handleError(error, 'immediateMessage', 'NETWORK');
          throw error;
        });
      }
      return Promise.reject(new Error('Chrome runtime not available'));
    },

    // 清空队列
    clearQueue() {
      this._queue = [];
      if (this._timer) {
        clearTimeout(this._timer);
        this._timer = null;
      }
    }
  },

  // 增强的性能监控系统
  monitor: {
    _metrics: new Map(),
    _startTimes: new Map(),

    // 开始性能测量
    start(name) {
      this._startTimes.set(name, performance.now());
    },

    // 结束性能测量并记录
    end(name) {
      const startTime = this._startTimes.get(name);
      if (!startTime) {
        Utils.handleError(new Error(`未找到性能测量起点: ${name}`), 'monitor', 'VALIDATION');
        return 0;
      }

      const duration = performance.now() - startTime;
      this._startTimes.delete(name);

      // 更新指标
      if (!this._metrics.has(name)) {
        this._metrics.set(name, {
          count: 0,
          totalTime: 0,
          minTime: Infinity,
          maxTime: 0,
          avgTime: 0
        });
      }

      const metric = this._metrics.get(name);
      metric.count++;
      metric.totalTime += duration;
      metric.minTime = Math.min(metric.minTime, duration);
      metric.maxTime = Math.max(metric.maxTime, duration);
      metric.avgTime = metric.totalTime / metric.count;

      return duration;
    },

    // 获取性能报告
    getReport(name = null) {
      if (name) {
        return this._metrics.get(name) || null;
      }
      
      const report = {};
      for (const [key, value] of this._metrics) {
        report[key] = { ...value };
      }
      return report;
    },

    // 清除指标
    clear(name = null) {
      if (name) {
        this._metrics.delete(name);
        this._startTimes.delete(name);
      } else {
        this._metrics.clear();
        this._startTimes.clear();
      }
    }
  },

  // 边界情况处理工具
  boundary: {
    // 安全访问对象属性
    safeAccess(obj, path, defaultValue = null) {
      if (!obj || typeof obj !== 'object') return defaultValue;
      
      try {
        return path.split('.').reduce((current, key) => {
          return (current && current[key] !== undefined) ? current[key] : defaultValue;
        }, obj);
      } catch (error) {
        Utils.handleError(error, 'safeAccess', 'BOUNDARY');
        return defaultValue;
      }
    },

    // 安全的数组操作
    safeArray(value, defaultValue = []) {
      return Array.isArray(value) ? value : defaultValue;
    },

    // 安全的数字转换
    safeNumber(value, defaultValue = 0, min = null, max = null) {
      const num = Number(value);
      if (isNaN(num)) return defaultValue;
      
      if (min !== null && num < min) return min;
      if (max !== null && num > max) return max;
      
      return num;
    },

    // 安全的字符串操作
    safeString(value, defaultValue = '', maxLength = null) {
      const str = String(value || defaultValue);
      return maxLength ? str.substring(0, maxLength) : str;
    },

    // 检查DOM节点有效性
    isValidNode(node) {
      return node && 
             node.nodeType && 
             document.contains && 
             document.contains(node);
    },

    // 安全的异步操作包装
    async safeAsync(asyncFn, fallback = null, timeout = 5000) {
      try {
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('操作超时')), timeout);
        });

        return await Promise.race([asyncFn(), timeoutPromise]);
      } catch (error) {
        Utils.handleError(error, 'safeAsync', 'ASYNC');
        return fallback;
      }
    },

    // 内存使用监控
    getMemoryUsage() {
      if (performance.memory) {
        return {
          used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
          total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
          limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
        };
      }
      return null;
    }
  }
};
