/**
 * 智能文本解析工具
 * 用于解读网络首字母缩写和翻译文本
 * powered by <PERSON><PERSON><PERSON><PERSON> @2025-03-25
 */
(function () {
  // 如果已经存在实例，先清理旧实例
  if (window.translatorInstance) {
    // 移除旧的DOM元素
    if (window.translatorInstance.popup) {
      window.translatorInstance.popup.remove();
    }
    if (window.translatorInstance.toolbar) {
      window.translatorInstance.toolbar.remove();
    }
    // 清理事件监听器
    document.removeEventListener(
      "dblclick",
      window.translatorInstance._handleDblClick
    );
    document.removeEventListener(
      "mouseup",
      window.translatorInstance._handleMouseUp
    );
    document.removeEventListener(
      "click",
      window.translatorInstance._handleClick
    );
  }

  class TextTranslator {
    constructor(options = {}) {
      // 保存事件处理函数的引用，以便后续能够移除
      this._handleDblClick = (e) => {
        // 如果是输入元素，不显示翻译工具栏
        if (this.isInputElement(e.target)) {
          return;
        }

        const selectedText = this.getSelectionText();
        if (selectedText && selectedText.trim().length > 0) {
          this.showToolbar(e);
        }
      };

      this._handleMouseUp = (e) => {
        // 如果是输入元素，不显示翻译工具栏
        if (this.isInputElement(e.target)) {
          return;
        }

        // 原有的处理逻辑
        const selection = window.getSelection();
        const selectedText = selection.toString().trim();

        if (selectedText.length > 0) {
          // 显示翻译工具栏的逻辑
          // ...
        }
      };

      this._handleClick = (e) => {
        // 如果正在拖动，则忽略点击事件
        if (this.isDragging) {
          this.isDragging = false; // 重置拖动状态
          return;
        }

        // 注释掉点击空白区域关闭弹窗的逻辑
        // 现在只能通过右上角的关闭按钮关闭弹窗
      };

      // 优化事件处理函数
      this._handleSelection = (e) => {
        // 忽略右键点击
        if (e.button === 2) return;

        // 如果是输入元素，不显示翻译工具栏
        if (this.isInputElement(e.target)) {
          return;
        }

        // 如果弹窗已经显示，不要显示工具栏
        if (this.popup && this.popup.style.display === "block") {
          return;
        }

        // 使用更可靠的方式获取选中文本，增加延迟确保选区已完全建立
        setTimeout(() => {
          const selectedText = this.getSelectionText();
          if (selectedText && selectedText.trim().length > 1) {
            this.showToolbar(e);
          }
        }, 50); // 增加延迟时间从10ms到50ms
      };

      // 缓存控制配置
      this.maxCacheSize = 100; // 最大缓存条目数
      this.cacheExpiration = 30 * 60 * 1000; // 30分钟过期

      // 请求队列控制
      this.maxQueueLength = 50;
      this.requestQueue = [];

      // 初始化缓存时间戳
      this.cache = {};
      this.guessCache = {};

      // 启动定期清理
      this.startCacheCleaner();

      // 创建翻译弹窗
      this.createTranslatorUI();

      // 添加工具栏启用状态属性
      this.toolbarEnabled = true;

      // 加载工具栏设置
      this.loadToolbarSettings();

      // 初始化事件监听
      this.initEventListeners();

      // 添加请求控制相关属性
      this.isProcessing = false;
      this.retryCount = 0;
      this.maxRetries = 2;
      this.retryDelay = 1000;
      this.requestInterval = 300;
      this.lastRequestTime = 0;

      // 添加nbnhhsh API配置
      this.nbnhhshConfig = {
        baseUrl: "https://lab.magiconch.com/api/nbnhhsh/",
        endpoints: {
          guess: "guess",
        },
      };

      // 添加缓存对象
      this.guessCache = {};

      // 统一的prompt配置
      this.prompts = {
        // 翻译文本的prompt
        translation: {
          zh: "您是一位精通各国语言的翻译专家，请将以下文本翻译成中文。请严格遵循以下规则：\n\n1. 如果输入是单个英文单词，请以词典格式列出多种常见词义和不同词性\n2. 如果输入是句子或段落，则只提供整体流畅的翻译，不要提取或解释任何单词\n3. 不要在句子或段落翻译中添加任何单词释义、注解或解释\n4. 句子翻译应当只有一个完整的中文句子，没有任何额外内容\n5. 对于包含敏感内容（如暴力、色情、歧视等）的文本，请在翻译前添加[敏感内容]标记，但仍然提供准确翻译",
        },
      };

      // 初始化基本配置，为两种类型都设置基本属性
      this.translationConfig = {
        className: "translator-tool",
        type: "translation", // 添加类型标识
      };

      this.abbrConfig = {
        className: "abbreviation-tool", // 添加类名
        type: "abbreviation", // 添加类型标识
      };

      this.proxyUrl = "https://api.geluman.cn/proxy.php"; // 确保这里没有参数

      // 直接加载配置
      this.loadServerConfig();

      // 在类中添加拖动状态标记
      this.isDragging = false; // 添加拖动状态标记
    }

    // 生成会话标识符
    generateSessionId() {
      return (
        Math.random().toString(36).substring(2, 15) +
        Math.random().toString(36).substring(2, 15)
      );
    }

    /**
     * 加载工具栏设置
     */
    async loadToolbarSettings() {
      try {
        // 从 Chrome 存储中读取工具栏设置
        const result = await chrome.storage.sync.get(
          "translator_toolbar_enabled"
        );
        if (result.translator_toolbar_enabled !== undefined) {
          this.toolbarEnabled = result.translator_toolbar_enabled;
          console.log(
            "划词工具栏设置已加载:",
            this.toolbarEnabled ? "启用" : "禁用"
          );

          // 立即应用设置
          if (!this.toolbarEnabled) {
            this.removeEventListeners();
          }
        }

        // 监听设置变化
        chrome.storage.onChanged.addListener((changes, area) => {
          if (area === "sync" && changes.translator_toolbar_enabled) {
            this.toolbarEnabled = changes.translator_toolbar_enabled.newValue;
            console.log(
              "划词工具栏设置已更新:",
              this.toolbarEnabled ? "启用" : "禁用"
            );

            // 如果设置变为禁用，则移除事件监听器
            if (!this.toolbarEnabled) {
              this.removeEventListeners();
            } else {
              // 如果设置变为启用，则添加事件监听器
              this.initEventListeners();
            }
          }
        });
      } catch (error) {
        console.error("加载工具栏设置失败:", error);
        // 出错时默认启用
        this.toolbarEnabled = true;
      }
    }

    /**
     * 初始化事件监听
     */
    initEventListeners() {
      // 如果工具栏被禁用，则不添加事件监听器
      if (!this.toolbarEnabled) {
        console.log("划词工具栏已禁用，不添加事件监听器");
        return;
      }

      // 移除可能存在的事件监听器，避免重复添加
      this.removeEventListeners();

      // 同时监听双击和鼠标抬起事件，使用捕获阶段以确保能捕获到所有事件
      document.addEventListener("dblclick", this._handleSelection, true);
      document.addEventListener(
        "mouseup",
        (e) => {
          // 只处理左键拖选
          if (e.button === 0) {
            // 延迟检查选区，确保选区已经建立
            setTimeout(() => {
              const selectedText = window.getSelection().toString().trim();
              if (selectedText.length > 1) {
                this._handleSelection(e);
              }
            }, 10);
          }
        },
        true
      ); // 使用捕获阶段

      // 点击空白处隐藏工具栏和弹窗
      document.addEventListener("click", this._handleClick);

      // 定期刷新配置（每小时检查一次）
      setInterval(() => this.loadServerConfig(), 60 * 60 * 1000);

      // 添加全局点击监听器，检查是否应该隐藏工具栏
      document.addEventListener("click", (e) => {
        // 如果工具栏显示着
        if (this.toolbar && this.toolbar.style.display !== "none") {
          // 检查点击是否在工具栏外部
          if (!this.toolbar.contains(e.target)) {
            // 检查点击是否在弹窗内部
            const clickedInPopup =
              this.popup &&
              this.popup.style.display !== "none" &&
              this.popup.contains(e.target);

            // 如果点击不在弹窗内，则隐藏工具栏
            if (!clickedInPopup) {
              this.hideToolbar();
            }
          }
        }
      });

      // 添加Escape键监听，按Escape键时关闭工具栏
      document.addEventListener("keydown", (e) => {
        if (
          e.key === "Escape" &&
          this.toolbar &&
          this.toolbar.style.display !== "none"
        ) {
          this.hideToolbar();
        }
      });

      // 添加窗口大小变化事件监听器
      window.addEventListener(
        "resize",
        () => {
          if (this.popup && this.popup.style.display === "block") {
            // 窗口大小变化时重新计算位置
            this.showPopup(this.currentTab);
          }
        },
        { passive: true }
      );

      // 保存最后一次事件
      document.addEventListener(
        "mouseup",
        (e) => {
          this.lastEvent = e;
        },
        { passive: true }
      );
    }

    /**
     * 移除事件监听器
     */
    removeEventListeners() {
      document.removeEventListener("dblclick", this._handleSelection, true);
      document.removeEventListener("mouseup", this._handleMouseUp, true);
      document.removeEventListener("click", this._handleClick);

      // 移除Escape键监听
      document.removeEventListener("keydown", (e) => {
        if (
          e.key === "Escape" &&
          this.toolbar &&
          this.toolbar.style.display !== "none"
        ) {
          this.hideToolbar();
        }
      });

      // 移除窗口大小变化事件监听器
      window.removeEventListener("resize", () => {
        if (this.popup && this.popup.style.display === "block") {
          // 窗口大小变化时重新计算位置
          this.showPopup(this.currentTab);
        }
      });

      // 隐藏工具栏和弹窗
      if (this.toolbar) {
        this.hideToolbar();
        // 完全移除工具栏
        this.toolbar.remove();
        this.toolbar = null;
      }
      if (this.popup) {
        this.hidePopup();
        // 完全移除弹窗
        this.popup.remove();
        this.popup = null;
      }
    }

    /**
     * 判断是否为点击事件
     */
    isClickEvent(e) {
      return (
        e.type === "mouseup" &&
        e.target.tagName !== "INPUT" &&
        e.target.tagName !== "TEXTAREA" &&
        window.getSelection().toString().length <= 1
      );
    }

    /**
     * 获取选中的文本
     */
    getSelectionText(cleanText = false) {
      const selection = window.getSelection();
      if (!selection.rangeCount) return "";

      // 尝试获取更可靠的选区文本
      let text = "";
      try {
        // 创建一个临时范围来获取选中的文本
        const range = selection.getRangeAt(0);
        const tempDiv = document.createElement("div");
        tempDiv.appendChild(range.cloneContents());
        text = tempDiv.textContent || tempDiv.innerText || "";
      } catch (e) {
        // 如果上述方法失败，回退到标准方法
        text = selection.toString();
      }

      text = text.trim();

      // 移除cleanText参数的处理，保留原始文本
      return text;
    }

    /**
     * 检查文本是否包含字母或数字
     */
    containsLettersOrNumbers(text) {
      return /[a-z0-9]/i.test(text);
    }

    /**
     * 显示工具栏
     */
    showToolbar(e) {
      // 创建新的工具栏
      this.createToolbar();

      const selection = window.getSelection();
      if (!selection.rangeCount) return;

      const range = selection.getRangeAt(0);
      const rect = range.getBoundingClientRect();

      // 优化位置计算，避免工具栏超出视窗
      const toolbar = this.toolbar;
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;

      let left = rect.left;
      let top = window.scrollY + rect.bottom + 10;

      // 确保工具栏不会超出右边界
      if (left + toolbar.offsetWidth > viewportWidth) {
        left = viewportWidth - toolbar.offsetWidth - 10;
      }

      // 确保工具栏不会超出底部
      if (top + toolbar.offsetHeight > window.scrollY + viewportHeight) {
        top = window.scrollY + rect.top - toolbar.offsetHeight - 10;
      }

      toolbar.style.left = `${left}px`;
      toolbar.style.top = `${top}px`;
      toolbar.style.display = "flex";
    }

    /**
     * 隐藏工具栏
     */
    hideToolbar() {
      if (this.toolbar) {
        this.toolbar.style.display = "none";
      }

      // 清除可能存在的timeout
      if (this.hideToolbarTimeout) {
        clearTimeout(this.hideToolbarTimeout);
        this.hideToolbarTimeout = null;
      }
    }

    /**
     * 隐藏翻译弹窗
     */
    hidePopup() {
      if (this.popup) {
        this.popup.style.display = "none";
      }
    }

    /**
     * 创建翻译器UI
     */
    createTranslatorUI() {
      // 创建样式
      const style = document.createElement("style");
      style.textContent = `
        /* 基础样式 */
        .translator-popup {
          position: absolute;
          width: 380px;
          max-width: 90vw;
          background-color: #fff;
          border-radius: 8px;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
          font-size: 14px;
          line-height: 1.5;
          color: #333;
          z-index: 9999;
          overflow: hidden;
          transition: opacity 0.2s ease;
        }
        
        /* 头部样式 */
        .translator-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 12px 16px;
          background-color: #f5f7fa;
          border-bottom: 1px solid #e8eaed;
          cursor: move;
          user-select: none;
        }
        
        .translator-header h4 {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: #333;
        }
        
        .translator-close {
          font-size: 20px;
          color: #666;
          cursor: pointer;
          padding: 0 4px;
        }
        
        .translator-close:hover {
          color: #333;
        }
        
        /* 内容区域样式 */
        .translator-content {
          padding: 16px;
          max-height: 400px;
          overflow-y: auto;
        }
        
        /* 标签页内容样式 */
        .translator-tab-content {
          display: none;
        }
        
        .translator-tab-content.active {
          display: block;
        }
        
        /* 加载状态样式 */
        .translator-loading {
          text-align: center;
          padding: 20px;
          color: #666;
        }
        
        /* 错误提示样式 */
        .translator-error {
          text-align: center;
          padding: 20px;
          color: #d73a49;
        }
        
        /* 翻译结果样式 */
        .translation-result {
          white-space: pre-wrap;
          word-break: break-word;
        }
        
        /* 解释结果样式 */
        #translator-explain-content {
          white-space: pre-wrap;
          word-break: break-word;
        }
        
        /* 敏感内容标记样式 */
        .sensitive-content {
                  display: inline-block;
                  background-color: #fff2f0;
                  color: #ff0000;  /* 改用正红色 */
                  border: 1px solid #ffccc7;
                  border-radius: 3px;
                  padding: 2px 6px;
                  margin: 0 2px;
                  font-size: 12px;
                  font-weight: 700;  /* 加粗文字 */
                  line-height: 1.4;
                  position: relative;
                }

                .sensitive-content::before {
                  content: "⚠️";
                  margin-right: 4px;
                  font-size: 12px;
                }
        
        /* 链接样式 */
        #translator-explain-content a,
        .translation-result a {
          color: #0366d6;
          text-decoration: none;
        }
        
        #translator-explain-content a:hover,
        .translation-result a:hover {
          text-decoration: underline;
        }
        
        /* 标题样式 */
        #translator-explain-content h1,
        #translator-explain-content h2,
        #translator-explain-content h3,
        .translation-result h1,
        .translation-result h2,
        .translation-result h3 {
          margin-top: 16px;
          margin-bottom: 8px;
          font-weight: 600;
          line-height: 1.25;
        }
        
        #translator-explain-content h1,
        .translation-result h1 {
          font-size: 18px;
        }
        
        #translator-explain-content h2,
        .translation-result h2 {
          font-size: 16px;
        }
        
        #translator-explain-content h3,
        .translation-result h3 {
          font-size: 14px;
        }
        
        /* 列表样式 */
        #translator-explain-content ul,
        #translator-explain-content ol,
        .translation-result ul,
        .translation-result ol {
          padding-left: 20px;
          margin: 8px 0;
        }
        
        #translator-explain-content li,
        .translation-result li {
          margin: 4px 0;
        }
        
        /* 强调和粗体样式 */
        #translator-explain-content strong,
        #translator-explain-content b,
        .translation-result strong,
        .translation-result b {
          font-weight: 600;
        }
        
        #translator-explain-content em,
        #translator-explain-content i,
        .translation-result em,
        .translation-result i {
          font-style: italic;
        }
        
        /* 底部样式 */
        .translator-footer {
          padding: 8px 16px;
          text-align: right;
          font-size: 12px;
          color: #666;
          background-color: #f5f7fa;
          border-top: 1px solid #e8eaed;
        }
        
        /* 工具栏样式 */
        .translator-toolbar {
          position: absolute;
          display: flex;
          background-color: #fff;
          border-radius: 4px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
          z-index: 9999;
          padding: 4px;
        }
        
        .translator-btn {
          display: flex;
          align-items: center;
          padding: 6px 10px;
          margin: 0 2px;
          border: none;
          border-radius: 4px;
          background-color: transparent;
          color: #333;
          font-size: 13px;
          cursor: pointer;
          transition: background-color 0.2s;
        }
        
        .translator-btn svg {
          margin-right: 4px;
        }
        
        .translator-btn:hover {
          background-color: #f1f1f1;
        }
        
        /* 统一按钮样式 */
        .abbreviation-btn, .explain-btn, .translate-btn {
          background-color: #f5f7fa;
          color: #333;
        }
        
        .abbreviation-btn:hover {
          background-color: #e8eaed;
          color: #8e44ad;
        }
        
        .explain-btn:hover {
          background-color: #e8eaed;
          color: #2ecc71;
        }
        
        .translate-btn:hover {
          background-color: #e8eaed;
          color: #3498db;
        }
        
        /* 缩写解释样式 */
        .abbr-title {
          margin-bottom: 12px;
          font-weight: 500;
          color: #333;
        }
        
        .abbr-meaning-list {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
          margin-bottom: 10px;
        }
        
        .abbr-meaning-item {
          background-color: #f1f8ff;
          color: #0366d6;
          padding: 4px 10px;
          border-radius: 4px;
          display: inline-block;
          font-size: 14px;
          border: 1px solid #daebff;
        }
        
        .abbr-meaning-item:hover {
          background-color: #daebff;
        }
      `;
      document.head.appendChild(style);

      // 创建弹窗
      const popup = document.createElement("div");
      popup.className = "translator-popup";
      popup.style.display = "none";

      // 设置弹窗HTML结构
      popup.innerHTML = `
        <div class="translator-header">
          <h4>文本翻译</h4>
          <span class="translator-close">&times;</span>
        </div>
        <div class="translator-content">
          <div id="translator-translate-content" class="translator-tab-content">
            <div class="translation-result"></div>
          </div>
          <div id="translator-explain-content" class="translator-tab-content">
            <!-- 解释内容区域 -->
          </div>
        </div>
        <div class="translator-footer">
          Powered by GLM-HIGHLIGHT
        </div>
      `;

      document.body.appendChild(popup);
      this.popup = popup;

      // 在弹窗添加到DOM后再绑定事件
      const closeBtn = popup.querySelector(".translator-close");
      if (closeBtn) {
        closeBtn.addEventListener("click", () => this.hidePopup());
      }

      // 创建工具栏
      this.createToolbar();

      // 添加拖动功能
      this.addDragSupport(popup);
    }

    // 新增方法：计算并应用合适的z-index
    calculateAndApplyZIndex() {
      if (!this.popup) return;

      try {
        // 获取当前选中文本所在元素
        const selection = window.getSelection();
        if (!selection.rangeCount) return;

        const range = selection.getRangeAt(0);
        const selectedNode = range.commonAncestorContainer;
        const selectedElement =
          selectedNode.nodeType === 1
            ? selectedNode
            : selectedNode.parentElement;

        // 获取选中元素的z-index和所有父元素
        let currentElement = selectedElement;
        let maxZIndex = 0;
        let computedZIndex;

        // 遍历父元素树查找最大z-index
        while (currentElement && currentElement !== document.body) {
          computedZIndex = window.getComputedStyle(currentElement).zIndex;

          // 只考虑有效的z-index值
          if (computedZIndex !== "auto" && !isNaN(parseInt(computedZIndex))) {
            maxZIndex = Math.max(maxZIndex, parseInt(computedZIndex));
          }

          currentElement = currentElement.parentElement;
        }

        // 设置弹窗z-index为找到的最大值+10（确保在元素之上，但不至于过高）
        // 如果未找到有效值，则使用一个较小的默认值
        this.popup.style.zIndex = maxZIndex > 0 ? maxZIndex + 10 : 1000;

        console.log(`已设置弹窗z-index为: ${this.popup.style.zIndex}`);
      } catch (error) {
        console.error("计算z-index时出错:", error);
        // 出错时使用相对安全的默认值
        this.popup.style.zIndex = 1000;
      }
    }

    /**
     * 初始化拖拽功能
     */
    initDraggable() {
      this.addDragSupport(this.toolbar);
    }

    /**
     * 翻译选中的文本（解读文本）
     */
    translateAbbr(fromToolbar = false) {
      const originalText = this.getSelectionText(false);
      if (!originalText) return;

      // 获取工具栏位置，这部分逻辑可能需要保留
      let toolbarRect = null;
      if (this.toolbar) {
        const rect = this.toolbar.getBoundingClientRect();
        toolbarRect = { ...rect }; // 复制rect属性
      }

      // 隐藏工具栏
      this.hideToolbar();
      window.getSelection().removeAllRanges();

      // 调用新的实现方法
      this.showPopup("abbr", true, toolbarRect);
      this.fetchNbnhhshExplanation(originalText);

      // 处理工具栏隐藏逻辑
      if (fromToolbar) {
        this.hideToolbarTimeout = setTimeout(() => {
          this.hideToolbar();
        }, 100);
      }
    }

    /**
     * 翻译选中的一般文本
     */
    translateText(fromToolbar = false) {
      const text = this.getSelectionText(false);
      if (!text) return;

      // 先获取工具栏位置并创建副本
      let toolbarRect = null;
      if (this.toolbar) {
        const rect = this.toolbar.getBoundingClientRect();
        // 保存完整的位置信息
        toolbarRect = {
          left: rect.left,
          top: rect.top,
          right: rect.right,
          bottom: rect.bottom,
          width: rect.width,
          height: rect.height,
          x: rect.x,
          y: rect.y,
        };
      }

      // 销毁工具栏
      this.hideToolbar();

      // 清除选中的文本
      window.getSelection().removeAllRanges();

      // 显示弹窗，传入保存的工具栏位置
      this.showPopup("translate", true, toolbarRect); // 强制使用 fromToolbar = true

      // 获取目标语言 - 现在总是返回'zh'
      const targetLang = this.getTargetLanguage();

      // 检查缓存
      const cacheKey = `translate_${text}_${targetLang}`;
      const cachedResult = this.getFromCache(cacheKey);
      if (cachedResult) {
        // 修改这里：使用renderTranslationResult而不是renderTranslation
        this.renderTranslationResult(cachedResult);
        return;
      }

      // 显示加载状态
      const content = this.popup.querySelector("#translator-translate-content");
      content.innerHTML = '<div class="translator-loading">翻译中...</div>';

      // 由于targetLang总是'zh'，可以简化API数据
      const apiData = {
        model: this.translationConfig.model,
        messages: [
          {
            role: "user",
            content: `请翻译以下文本，严格遵循这些规则：\n1. 如果是单个英文单词，列出多种词义和词性\n2. 如果是句子或段落，只提供完整翻译，不要包含任何单词释义\n3. 句子翻译必须只有翻译结果，没有任何额外解释\n4. 对于包含敏感内容（如暴力、色情、歧视等）的文本，请在翻译前添加[敏感内容]标记，但仍然提供准确翻译\n\n${text}`,
          },
        ],
      };

      this.callProxyApi(this.translationConfig.model, apiData)
        .then((response) => {
          // 添加更严格的响应检查
          if (
            response &&
            response.choices &&
            response.choices[0] &&
            response.choices[0].message &&
            response.choices[0].message.content
          ) {
            const translation = response.choices[0].message.content.trim();
            this.addToCache(cacheKey, translation);

            // 使用正确的方法渲染结果
            this.renderTranslationResult(translation);
          } else {
            content.innerHTML =
              '<div class="translator-error">翻译接口返回无效数据</div>';
            console.error("API返回数据结构异常:", response);
          }
        })
        .catch((error) => {
          console.error("翻译出错:", error);
          content.innerHTML =
            '<div class="translator-error">翻译失败，请稍后再试</div>';
        });

      // 如果是从工具栏调用的，则隐藏工具栏
      if (fromToolbar) {
        // 保存setTimeout的ID，这样可以在必要时清除它
        this.hideToolbarTimeout = setTimeout(() => {
          this.hideToolbar();
        }, 100); // 略微延迟以确保事件处理完成
      }
    }

    /**
     * 添加样式方法
     */
    addStyles() {
      if (!document.querySelector("#translator-additional-styles")) {
        const style = document.createElement("style");
        style.id = "translator-additional-styles";
        style.textContent = `
          .translator-ai-result {
            padding: 10px;
            margin: 5px 0;
            background-color: rgba(240, 240, 240, 0.5);
            border-radius: 4px;
            line-height: 1.5;
            white-space: pre-wrap;
          }
          .translator-result-container {
            max-height: 300px;
            overflow-y: auto;
            margin-top: 10px;
          }
          .translator-source-tag {
            display: inline-block;
            background: #f0f0f0;
            padding: 2px 6px;
            border-radius: 3px;
            color: #555;
            font-size: 12px;
            margin-bottom: 5px;
          }
          .translator-query-text {
            font-size: 13px;
            color: #666;
            margin-bottom: 10px;
          }
          .negative-content-tag {
            color: #d73a49;
            font-weight: bold;
            background-color: rgba(215, 58, 73, 0.1);
            padding: 2px 4px;
            border-radius: 3px;
            margin-right: 4px;
          }
          .translator-ai-result ul {
            margin: 0;
            padding-left: 20px;
          }
          .translator-ai-result li {
            margin-bottom: 5px;
          }
        `;
        document.head.appendChild(style);
      }

      // 在addStyles方法中修改敏感内容的警示样式
      const sensitiveStyle = `
        .sensitive-content {
          display: inline-block;
          background-color: #fff2f0;
          color: #ff0000;  /* 改用正红色 */
          border: 1px solid #ffccc7;
          border-radius: 3px;
          padding: 2px 6px;
          margin: 0 2px;
          font-size: 12px;
          font-weight: 700;  /* 加粗文字 */
          line-height: 1.4;
          position: relative;
        }
        
        .sensitive-content::before {
          content: "⚠️";
          margin-right: 4px;
          font-size: 12px;
        }
        
        .translator-ai-result {
          color: #333;  /* 保持普通文本颜色 */
          line-height: 1.6;
        }
        
        .abbr-details {
          color: #333;  /* 保持普通文本颜色 */
          line-height: 1.6;
        }
      `;
    }

    /**
     * 格式化文本为HTML
     */
    formatTextToHtml(text) {
      // 确保text是字符串
      if (text === undefined || text === null) {
        return "";
      }

      // 将text转换为字符串
      const safeText = String(text);

      // 处理换行
      return safeText
        .replace(/\n/g, "<br>")
        .replace(/\t/g, "&nbsp;&nbsp;&nbsp;&nbsp;");
    }

    /**
     * 显示错误提示
     */
    showError(message) {
      const content = this.popup.querySelector("#translator-abbr-content");
      content.innerHTML = `
        <div class="translator-error">
          ${message}
          <button class="retry-btn">重试</button>
        </div>
      `;

      // 添加重试按钮事件
      const retryBtn = content.querySelector(".retry-btn");
      if (retryBtn) {
        retryBtn.addEventListener("click", () => {
          this.retryCount = 0;
          this.processRequestQueue();
        });
      }
    }

    /**
     * 渲染翻译结果
     */
    renderTranslationResult(translationText) {
      const content = this.popup.querySelector("#translator-translate-content");

      // 清除加载状态
      content.innerHTML = "";

      // 检查翻译文本是否有效
      if (translationText === undefined || translationText === null) {
        content.innerHTML =
          '<div class="translator-error">获取到的翻译结果无效</div>';
        return;
      }

      // 确保translationText是字符串
      const safeText = String(translationText);

      // 创建翻译结果容器
      const resultContainer = document.createElement("div");
      resultContainer.className = "translator-result-container";

      try {
        // 使用专门的格式化方法，不处理敏感内容标记
        resultContainer.innerHTML = this.formatTranslationTextToHtml(safeText);
      } catch (error) {
        console.error("格式化翻译结果出错:", error);
        resultContainer.textContent = safeText; // 降级为纯文本显示
      }

      // 添加到内容区域
      content.appendChild(resultContainer);
    }

    /**
     * 执行翻译文本
     */
    performTranslation(text) {
      // 简化：不再获取用户选择的目标语言，直接使用固定语言
      const targetLang = "zh"; // 固定使用中文作为目标语言

      // 使用简化的语言提示
      const languagePrompt = this.getLanguagePrompt(targetLang);

      // 改进缓存键，不再需要包含目标语言变量
      const cacheKey = `translate_${text}`;
      if (this.cache[cacheKey]) {
        this.renderTranslationResult(this.cache[cacheKey]);
        return;
      }

      // 简化：直接显示加载状态，不再包含语言选择器
      const content = this.popup.querySelector("#translator-translate-content");
      content.innerHTML = '<div class="translator-loading">翻译中...</div>';

      // API请求和处理部分保持不变
      const apiData = {
        model: this.translationConfig.model,
        messages: [
          {
            role: "user",
            content: `${languagePrompt}\n\n${text}`,
          },
        ],
      };
    }

    /**
     * 显示翻译错误
     */
    showTranslationError() {
      const content = this.popup.querySelector("#translator-translate-content");
      content.innerHTML = `
        <div class="translator-error">翻译请求失败，请稍后再试</div>
      `;
    }

    /**
     * 转义HTML特殊字符
     */
    escapeHtml(text) {
      if (!text) return "";
      return String(text)
        .replace(/&/g, "&amp;")
        .replace(/</g, "&lt;")
        .replace(/>/g, "&gt;")
        .replace(/"/g, "&quot;")
        .replace(/'/g, "&#039;");
    }

    /**
     * 新增统一的弹窗定位工具函数
     */
    getPopupPosition(rect, popupWidth, popupHeight) {
      // 计算初步位置（下方优先）
      let left = rect.left;
      let top = rect.bottom + 8;

      // 右边界校正
      if (left + popupWidth > window.innerWidth - 10) {
        left = window.innerWidth - popupWidth - 10;
      }
      if (left < 10) left = 10;

      // 下边界校正
      if (top + popupHeight > window.innerHeight - 10) {
        // 尝试放到选区上方
        if (rect.top - popupHeight - 8 > 10) {
          top = rect.top - popupHeight - 8;
        } else {
          // 还是不够就贴底
          top = window.innerHeight - popupHeight - 10;
        }
      }
      if (top < 10) top = 10;

      return { left, top };
    }

    // 弹窗显示后再次校正
    keepPopupInView(popup) {
      const rect = popup.getBoundingClientRect();
      let left = rect.left,
        top = rect.top;
      let changed = false;

      if (rect.right > window.innerWidth - 10) {
        left = window.innerWidth - rect.width - 10;
        changed = true;
      }
      if (rect.left < 10) {
        left = 10;
        changed = true;
      }
      if (rect.bottom > window.innerHeight - 10) {
        top = window.innerHeight - rect.height - 10;
        changed = true;
      }
      if (rect.top < 10) {
        top = 10;
        changed = true;
      }
      if (changed) {
        popup.style.left = `${left + window.scrollX}px`;
        popup.style.top = `${top + window.scrollY}px`;
      }
    }

    /**
     * 显示翻译弹窗并激活指定标签
     * @param {string} tabName - 要激活的标签名称 ('abbr' 或 'translate')
     */
    showPopup(tab = "translate", forceShow = false, toolbarRect = null) {
      // 如果弹窗不存在，创建弹窗
      if (!this.popup) {
        this.createTranslatorUI();
      }

      // 设置当前标签
      this.currentTab = tab;

      // 更新标题
      const title = this.popup.querySelector(".translator-header h4");
      if (title) {
        if (tab === "translate") {
          title.textContent = "文本翻译";
        } else if (tab === "explain") {
          title.textContent = "文本解读";
        } else if (tab === "abbreviation") {
          title.textContent = "缩写解释";
        }
      }

      // 显示相应的内容区域
      const translateContent = this.popup.querySelector(
        "#translator-translate-content"
      );
      const explainContent = this.popup.querySelector(
        "#translator-explain-content"
      );

      if (tab === "translate") {
        translateContent.style.display = "block";
        explainContent.style.display = "none";
      } else {
        translateContent.style.display = "none";
        explainContent.style.display = "block";
      }

      // 计算弹窗尺寸
      this.popup.style.display = "block";
      this.popup.style.opacity = "0";
      const popupWidth = this.popup.offsetWidth || 380;
      const popupHeight = this.popup.offsetHeight || 200;

      let rect;
      if (forceShow && toolbarRect) {
        rect = toolbarRect;
      } else {
        const selection = window.getSelection();
        if (!selection.rangeCount) return;
        rect = selection.getRangeAt(0).getBoundingClientRect();
      }

      // 统一使用工具函数计算位置
      const { left, top } = this.getPopupPosition(
        rect,
        popupWidth,
        popupHeight
      );

      this.popup.style.position = "fixed";
      this.popup.style.left = `${left}px`;
      this.popup.style.top = `${top}px`;
      this.popup.style.opacity = "1";

      // 显示后再次校正
      setTimeout(() => this.keepPopupInView(this.popup), 0);
    }

    // 添加请求队列处理方法
    async processRequestQueue() {
      if (this.isProcessing || this.requestQueue.length === 0) return;

      this.isProcessing = true;

      while (this.requestQueue.length > 0) {
        const request = this.requestQueue[0];

        // 检查请求间隔
        const now = Date.now();
        const timeSinceLastRequest = now - this.lastRequestTime;
        if (timeSinceLastRequest < this.requestInterval) {
          await new Promise((resolve) =>
            setTimeout(resolve, this.requestInterval - timeSinceLastRequest)
          );
        }

        try {
          await this.executeRequest(request);
          this.requestQueue.shift(); // 移除已处理的请求
          this.lastRequestTime = Date.now();
          this.retryCount = 0; // 重置重试计数
        } catch (error) {
          if (this.retryCount < this.maxRetries) {
            this.retryCount++;
            await new Promise((resolve) =>
              setTimeout(resolve, this.retryDelay)
            );
            continue; // 重试当前请求
          } else {
            this.requestQueue.shift(); // 移除失败的请求
            this.retryCount = 0;
            this.showError("请求失败，请稍后重试");
          }
        }
      }

      this.isProcessing = false;
    }

    // 执行API请求的方法
    async executeRequest(request) {
      const { url, options, callback } = request;

      try {
        // 添加错误重试计数
        let retries = 0;
        const maxRetries = 3;
        const retryDelay = 1000;

        while (retries < maxRetries) {
          try {
            // 添加 mode: 'cors' 和超时控制
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 5000); // 5秒超时

            const response = await fetch(url, {
              ...options,
              mode: "cors", // 明确指定 CORS 模式
              signal: controller.signal,
              headers: {
                ...options.headers,
                Accept: "application/json",
                "Access-Control-Allow-Origin": "*",
              },
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
              throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            callback(data);
            return;
          } catch (error) {
            retries++;
            if (retries === maxRetries) {
              throw error;
            }
            // 等待一段时间后重试
            await new Promise((resolve) =>
              setTimeout(resolve, retryDelay * retries)
            );
          }
        }
      } catch (error) {
        console.error("Request failed:", error);

        // 根据错误类型显示不同的错误信息
        let errorMessage = "请求失败，请稍后重试";
        if (error.name === "AbortError") {
          errorMessage = "请求超时，请检查网络连接";
        } else if (error.message.includes("Failed to fetch")) {
          errorMessage = "网络请求失败，可能是跨域限制或网络问题";
        }

        // 如果是 nbnhhsh API 失败，直接使用 AI 回退
        if (url.includes("nbnhhsh")) {
          const text = JSON.parse(options.body).text;
          this.fallbackToAI(text);
          return;
        }

        this.showError(errorMessage);
        throw error;
      }
    }

    /**
     * 获取语言提示模板
     * @param {string} targetLang - 目标语言 ('zh' 或 'en')
     * @returns {string} - 返回对应的提示模板
     */
    getLanguagePrompt(targetLang = "zh") {
      // 由于现在只支持中文翻译，可以直接返回中文提示
      return this.prompts.translation.zh;
    }

    // 预加载方法
    preloadNbnhhsh(text) {
      if (this.guessCache[text]) return;

      this.requestNbnhhsh("POST", "guess", { text })
        .then((data) => {
          if (data) {
            this.guessCache[text] = data;
          }
        })
        .catch(() => {}); // 忽略错误
    }

    // 新增 querySingleWord 方法处理单个词的查询
    async querySingleWord(word) {
      // 清理单个词中的标点符号
      const queryText = word.replace(/[^\w\u4e00-\u9fa5]/g, "").trim();

      if (!queryText) return null;

      // 检查缓存
      if (this.guessCache[queryText]) {
        return this.guessCache[queryText];
      }

      try {
        const response = await fetch(
          this.nbnhhshConfig.baseUrl + this.nbnhhshConfig.endpoints.guess,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            mode: "cors",
            body: JSON.stringify({ text: queryText }),
          }
        );

        if (!response.ok) {
          return null;
        }

        const data = await response.json();

        if (!data || !data.length) {
          return null;
        }

        // 只缓存有效结果
        if (this.hasValidExplanations(data)) {
          this.guessCache[queryText] = data;
        }

        return data;
      } catch (error) {
        console.error("nbnhhsh查询失败:", error);
        return null;
      }
    }

    // 代理API调用方法 - 完整实现
    async callProxyApi(model, data, options = {}) {
      // 确保配置已加载
      await this.ensureConfigLoaded();

      const maxRetries = 3;
      let retryCount = 0;

      const tryRequest = async () => {
        try {
          console.log(
            `[API请求] 尝试第 ${retryCount + 1}/${maxRetries} 次请求...`
          );

          // 构建请求URL
          const url = new URL(this.proxyUrl);

          // 如果使用服务器默认配置，添加标记
          if (this.translationConfig.useServerDefault) {
            url.searchParams.append("use_default", "1");
          }
          // 否则添加模型参数
          else if (model) {
            url.searchParams.append("model", model);
          } else {
            // 确保总是有模型参数或use_default参数
            throw new Error("未指定模型且未启用默认配置");
          }

          // 添加时间戳防止缓存
          url.searchParams.append("t", Date.now());

          const requestOptions = {
            method: options.method || "POST",
            headers: {
              Accept: "application/json",
              "Content-Type": "application/json",
              "Cache-Control": "no-cache",
            },
            mode: "cors",
          };

          // 重要：检查数据是否有效
          if (requestOptions.method === "POST") {
            if (
              !data ||
              (typeof data === "object" && Object.keys(data).length === 0)
            ) {
              throw new Error("POST请求缺少有效数据");
            }

            // 确保data是有效的对象
            requestOptions.body = JSON.stringify(data);

            // 增加调试日志
            console.log("[API请求] POST数据:", {
              url: url.toString(),
              headers: requestOptions.headers,
              data: JSON.parse(JSON.stringify(data)), // 创建副本以避免循环引用
            });
          }

          const response = await fetch(url.toString(), requestOptions);

          if (!response.ok) {
            // 获取错误详情
            let errorDetail = "";
            try {
              const errorJson = await response.json();
              errorDetail = JSON.stringify(errorJson);
            } catch (e) {
              try {
                errorDetail = await response.text();
              } catch (textError) {
                errorDetail = "无法获取响应内容: " + textError.message;
              }
            }

            // 记录完整的请求信息以便调试
            console.error("请求失败详情:", {
              url: url.toString(),
              method: requestOptions.method,
              headers: requestOptions.headers,
              body: requestOptions.body
                ? JSON.parse(requestOptions.body)
                : null,
              status: response.status,
              statusText: response.statusText,
              responseDetail: errorDetail,
            });

            throw new Error(`HTTP错误: ${response.status} - ${errorDetail}`);
          }

          return await response.json();
        } catch (error) {
          console.error(
            `[API请求] 失败 (尝试 ${retryCount + 1}/${maxRetries}):`,
            error
          );

          if (retryCount < maxRetries - 1) {
            retryCount++;
            const delay = Math.pow(2, retryCount) * 1000;
            console.log(`[API请求] 将在 ${delay}ms 后重试...`);
            await new Promise((resolve) => setTimeout(resolve, delay));
            return tryRequest();
          } else {
            throw error;
          }
        }
      };

      return tryRequest();
    }

    // 合并后的单一实现
    async diagnoseProblem() {
      // 合并两个实现的最完整版本
    }

    /**
     * 加载服务器配置
     */
    async loadServerConfig() {
      try {
        console.log("[配置] 开始加载服务器配置...");

        // 构建配置URL
        const configUrl = `${this.proxyUrl}?config=models`;

        console.log("[配置] 请求URL:", configUrl);

        // 发送请求
        const response = await fetch(configUrl, {
          method: "GET",
          headers: {
            Accept: "application/json",
            "Cache-Control": "no-cache",
          },
        });

        if (!response.ok) {
          throw new Error(`服务器响应错误: ${response.status}`);
        }

        const config = await response.json();
        console.log("[配置] 服务器返回配置:", config);

        // 检查配置格式
        if (!config || config.status !== "success" || !config.config) {
          throw new Error("配置数据格式不正确");
        }

        // 保存配置
        if (config.config.translation) {
          this.translationConfig = {
            ...this.translationConfig,
            ...config.config.translation,
          };
          console.log("[配置] 已加载翻译配置:", this.translationConfig);
        }

        this.configReady = true;
        return true;
      } catch (error) {
        console.error("[配置] 加载失败:", error.message);

        if (!this.translationConfig.default_model) {
          console.log("[配置] 使用默认翻译模型");
          // 不硬编码模型名称，而是使用标记
          this.translationConfig.useServerDefault = true;
        }

        this.configReady = true;
        return false;
      }
    }

    // 添加后备配置方法
    useBackupConfig() {
      // 不再使用本地硬编码的配置
      // 而是使用一个简单的标记，表示使用服务器端的默认配置
      this.translationConfig = {
        ...this.translationConfig,
        useServerDefault: true,
      };

      this.abbrConfig = {
        useServerDefault: true,
      };

      this.configReady = true;

      // 尝试请求服务器默认配置
      this.fetchServerDefaultConfig();
    }

    // 添加新方法获取服务器默认配置
    async fetchServerDefaultConfig() {
      try {
        const response = await fetch(`${this.proxyUrl}?default=1`, {
          method: "GET",
          headers: {
            Accept: "application/json",
          },
          cache: "no-store",
        });

        if (response.ok) {
          const defaultConfig = await response.json();
          if (defaultConfig && defaultConfig.config) {
            // 更新配置但不覆盖useServerDefault标记
            this.translationConfig = {
              ...this.translationConfig,
              ...defaultConfig.config.translation,
            };

            this.abbrConfig = {
              ...this.abbrConfig,
              ...defaultConfig.config.abbreviation,
            };
          }
        }
      } catch (error) {
        console.warn("[默认配置] 获取失败:", error.message);
      }
    }

    // 添加诊断方法
    async diagnoseProblem() {
      try {
        // 1. 检查代理服务器是否可访问
        const testResponse = await fetch(this.proxyUrl, {
          method: "GET",
          headers: {
            Accept: "application/json",
          },
        });

        if (!testResponse.ok) {
          return `代理服务器响应异常: ${testResponse.status}`;
        }

        // 2. 检查配置接口
        const configUrl = new URL(this.proxyUrl);
        configUrl.searchParams.append("config", "models");

        const configResponse = await fetch(configUrl.toString(), {
          method: "GET",
          headers: {
            Accept: "application/json",
          },
        });

        if (!configResponse.ok) {
          return `配置接口异常: ${configResponse.status}`;
        }

        // 3. 检查响应格式
        const data = await configResponse.json();
        if (!data || !data.status) {
          return "配置数据格式异常";
        }

        return "服务正常，但配置加载失败，可能是数据格式问题";
      } catch (error) {
        if (error.name === "TypeError" && error.message === "Failed to fetch") {
          return "网络请求失败，可能是CORS或网络连接问题";
        }
        return `诊断失败: ${error.message}`;
      }
    }

    // 修改 ensureConfigLoaded 方法
    async ensureConfigLoaded() {
      if (!this.configReady) {
        const success = await this.loadServerConfig();
        if (!success) {
          console.warn("[配置] 使用后备配置继续运行");
        }
        return true; // 即使使用后备配置也返回true，允许继续运行
      }
      return true;
    }

    // 在调用 API 之前添加配置检查
    async callProxyApi(model, data, options = {}) {
      // 确保配置已加载
      await this.ensureConfigLoaded();

      const maxRetries = 3;
      let retryCount = 0;

      const tryRequest = async () => {
        try {
          // 构建请求URL
          const url = new URL(this.proxyUrl);

          // 添加模型参数
          if (model) {
            url.searchParams.append("model", model);
          }

          // 添加时间戳防止缓存
          url.searchParams.append("t", Date.now());

          const response = await fetch(url.toString(), {
            method: "POST",
            headers: {
              Accept: "application/json",
              "Content-Type": "application/json",
              "Cache-Control": "no-cache",
            },
            mode: "cors",
            body: JSON.stringify(data),
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          return await response.json();
        } catch (error) {
          if (retryCount < maxRetries - 1) {
            retryCount++;
            const delay = Math.pow(2, retryCount) * 1000;
            await new Promise((resolve) => setTimeout(resolve, delay));
            return tryRequest();
          }
          throw error;
        }
      };

      return tryRequest();
    }

    // 添加公共方法，允许手动刷新配置
    async refreshConfig() {
      // 移除: console.log("手动刷新配置...");
      this.configReady = false;
      await this.loadServerConfig();
      this.configReady = true;
      // 移除: console.log("配置刷新完成");
      return true;
    }

    // 新增 createToolbar 方法,用于创建工具栏
    createToolbar() {
      // 如果已经存在工具栏，先移除
      if (this.toolbar) {
        this.toolbar.remove();
      }

      // 创建工具栏
      const toolbar = document.createElement("div");
      toolbar.className = "translator-toolbar";
      toolbar.style.display = "none";
      document.body.appendChild(toolbar);

      // 创建缩写解释按钮
      const abbreviationBtn = document.createElement("button");
      abbreviationBtn.className = "translator-btn abbreviation-btn";
      abbreviationBtn.innerHTML = `
        <svg viewBox="0 0 24 24" width="16" height="16">
          <path fill="currentColor" d="M4,2H20A2,2 0 0,1 22,4V16A2,2 0 0,1 20,18H16L12,22L8,18H4A2,2 0 0,1 2,16V4A2,2 0 0,1 4,2M4,4V16H8.83L12,19.17L15.17,16H20V4H4M6,7H18V9H6V7M6,11H16V13H6V11Z"/>
        </svg>
        缩写解释
      `;
      abbreviationBtn.addEventListener("click", () =>
        this.explainAbbreviation()
      );
      toolbar.appendChild(abbreviationBtn);

      // 创建翻译按钮
      const translateBtn = document.createElement("button");
      translateBtn.className = "translator-btn translate-btn";
      translateBtn.innerHTML = `
        <svg viewBox="0 0 24 24" width="16" height="16">
          <path fill="currentColor" d="M12.87,15.07L10.33,12.56L10.36,12.53C12.1,10.59 13.34,8.36 14.07,6H17V4H10V2H8V4H1V6H12.17C11.5,7.92 10.44,9.75 9,11.35C8.07,10.32 7.3,9.19 6.69,8H4.69C5.42,9.63 6.42,11.17 7.67,12.56L2.58,17.58L4,19L9,14L12.11,17.11L12.87,15.07M18.5,10H16.5L12,22H14L15.12,19H19.87L21,22H23L18.5,10M15.88,17L17.5,12.67L19.12,17H15.88Z"/>
        </svg>
        文本翻译
      `;
      translateBtn.addEventListener("click", () => this.translateText());
      toolbar.appendChild(translateBtn);

      this.toolbar = toolbar;
    }

    // 添加缓存管理方法
    addToCache(key, value, cacheType = "normal") {
      const cache = cacheType === "guess" ? this.guessCache : this.cache;

      // 检查缓存大小
      if (Object.keys(cache).length >= this.maxCacheSize) {
        // 删除最旧的缓存
        const oldestKey = Object.keys(cache).reduce((a, b) =>
          cache[a].timestamp < cache[b].timestamp ? a : b
        );
        delete cache[oldestKey];
      }

      // 添加新缓存，包含时间戳
      cache[key] = {
        data: value,
        timestamp: Date.now(),
      };
    }

    getFromCache(key, cacheType = "normal") {
      const cache = cacheType === "guess" ? this.guessCache : this.cache;
      const item = cache[key];

      if (!item) return null;

      // 检查是否过期
      if (Date.now() - item.timestamp > this.cacheExpiration) {
        delete cache[key];
        return null;
      }

      return item.data;
    }

    clearExpiredCache() {
      const now = Date.now();
      const clearCache = (cache) => {
        Object.keys(cache).forEach((key) => {
          if (now - cache[key].timestamp > this.cacheExpiration) {
            delete cache[key];
          }
        });
      };

      clearCache(this.cache);
      clearCache(this.guessCache);
    }

    startCacheCleaner() {
      // 每5分钟清理一次过期缓存
      this.cacheCleanerInterval = setInterval(() => {
        this.clearExpiredCache();
      }, 5 * 60 * 1000);
    }

    // 修改 destroy 方法，完善实例销毁机制
    destroy() {
      // 清理定时器
      if (this.cacheCleanerInterval) {
        clearInterval(this.cacheCleanerInterval);
        this.cacheCleanerInterval = null;
      }

      // 取消所有进行中的请求
      this.requestQueue.forEach((request) => {
        request.controller?.abort();
      });
      this.requestQueue = [];

      // 清理所有缓存
      this.cache = {};
      this.guessCache = {};

      // 清理DOM元素
      if (this.popup) {
        this.popup.remove();
        this.popup = null;
      }
      if (this.toolbar) {
        this.toolbar.remove();
        this.toolbar = null;
      }

      // 移除所有事件监听器
      this._removeAllEventListeners();

      // 清理样式表
      const styleElement = document.getElementById("translator-style");
      if (styleElement) {
        styleElement.remove();
      }

      // 在销毁时移除事件监听
      window.removeEventListener("scroll", this.scrollHandler);
    }

    // 添加 getTargetLanguage 方法
    getTargetLanguage() {
      // 由于不再支持语言切换，直接返回默认语言
      return "zh"; // 或者您需要的固定语言代码
    }

    // 添加新方法：专门用于翻译结果的格式化，不处理敏感内容
    formatTranslationTextToHtml(text) {
      // 确保text是字符串
      if (text === undefined || text === null) {
        return "";
      }

      // 将text转换为字符串
      const safeText = String(text);

      // 处理敏感内容标记和基本格式
      return safeText
        .replace(
          /\[敏感内容\]/g,
          '<span class="sensitive-content">⚠️ 敏感内容</span>'
        )
        .replace(/\n/g, "<br>")
        .replace(/\t/g, "&nbsp;&nbsp;&nbsp;&nbsp;");
    }

    /**
     * 调整弹窗大小以适应内容
     * 新增方法以确保窗口适应内容
     */
    adjustPopupSize() {
      // 内容加载完成后调整弹窗大小
      setTimeout(() => {
        if (!this.popup) return;

        const content = this.popup.querySelector(".translator-content.active");
        if (!content) return;

        // 临时移除max-height限制以获取完整内容高度
        const originalMaxHeight = content.style.maxHeight;
        content.style.maxHeight = "none";

        // 获取内容实际高度
        const contentHeight = content.scrollHeight;

        // 确保内容不超过视窗高度的90%
        const maxHeight = window.innerHeight * 0.9 - 50; // 减去头部和边距高度
        const newHeight = Math.min(contentHeight, maxHeight);

        // 还原max-height设置
        content.style.maxHeight = originalMaxHeight;

        // 更新弹窗位置和大小
        this.showPopup(this.currentTab, false);
      }, 50);
    }

    /**
     * 检查元素是否为输入类型元素
     * @param {Element} element - 要检查的DOM元素
     * @returns {boolean} - 如果是输入类型元素返回true，否则返回false
     */
    isInputElement(element) {
      // 1. 检查元素本身是否是输入元素
      if (
        // 标准输入元素
        element.tagName === "INPUT" ||
        element.tagName === "TEXTAREA" ||
        element.tagName === "SELECT" ||
        // 可编辑元素
        element.isContentEditable ||
        element.getAttribute("contenteditable") === "true" ||
        // ARIA角色
        element.getAttribute("role") === "textbox" ||
        element.getAttribute("role") === "searchbox" ||
        element.getAttribute("role") === "combobox"
      ) {
        return true;
      }

      // 2. 检查元素是否在表单中，但排除只读表单
      if (element.closest("form") && !element.hasAttribute("readonly")) {
        return true;
      }

      // 3. 检查元素是否在可编辑区域内
      let parent = element.parentElement;
      while (parent) {
        if (
          parent.isContentEditable ||
          parent.getAttribute("contenteditable") === "true" ||
          (parent.tagName === "INPUT" && !parent.hasAttribute("readonly")) ||
          (parent.tagName === "TEXTAREA" && !parent.hasAttribute("readonly"))
        ) {
          return true;
        }
        parent = parent.parentElement;
      }

      // 4. 检查元素是否有输入相关的事件监听器，但排除高亮元素
      if (
        element.classList &&
        Array.from(element.classList).some((cls) => cls.includes("highlight"))
      ) {
        return false; // 高亮元素不应被视为输入元素
      }

      const elementStyle = window.getComputedStyle(element);
      if (
        elementStyle.cursor === "text" &&
        !element.classList.contains("highlight") && // 排除高亮元素
        elementStyle.userSelect !== "none" // 排除不可选择的元素
      ) {
        return true;
      }

      return false;
    }

    /**
     * 格式化nbnhhsh结果
     * @param {Array} results - nbnhhsh查询结果
     * @returns {Array} - 格式化后的结果供UI使用
     */
    formatNbnhhshResult(results) {
      // 直接返回处理过的原始数据，供渲染函数使用
      return results.map((item) => {
        // 确保每个项都有trans和inputting数组
        if (!item.trans) item.trans = [];
        if (!item.inputting) item.inputting = [];

        // 添加一个标记，表示是否有解释
        item.hasExplanations =
          (item.trans && item.trans.length > 0) ||
          (item.inputting && item.inputting.length > 0);

        return item;
      });
    }

    /**
     * 检查nbnhhsh结果是否有有效解释
     * @param {Array} results - nbnhhsh查询结果
     * @returns {boolean} - 如果有有效解释返回true，否则返回false
     */
    hasValidExplanations(results, type = "default") {
      if (!results || !results.length) return false;

      return results.some((item) => {
        // 检查是否有有效的翻译
        const hasTrans = item.trans && item.trans.length > 0;
        // 检查是否有可能的输入
        const hasInputting = item.inputting && item.inputting.length > 0;

        return hasTrans || hasInputting;
      });
    }

    /**
     * 计算弹窗位置
     * @param {Event|Object} event - 触发事件或包含位置信息的对象
     * @returns {Object} - 包含left和top属性的位置对象
     */
    calculatePopupPosition(event) {
      // 获取窗口尺寸
      const windowWidth = window.innerWidth;
      const windowHeight = window.innerHeight;

      // 获取弹窗尺寸
      const popupWidth = this.popup.offsetWidth || 400; // 默认宽度
      const popupHeight = this.popup.offsetHeight || 300; // 默认高度

      // 计算初始位置 - 优先使用选区位置
      let left, top;

      const selection = window.getSelection();
      if (selection && selection.rangeCount > 0) {
        // 使用选区位置
        const range = selection.getRangeAt(0);
        const rect = range.getBoundingClientRect();

        // 初始位置在选区下方
        left = rect.left + window.scrollX;
        top = rect.bottom + window.scrollY + 10; // 添加10px间距
      } else if (event) {
        // 使用事件位置
        left =
          (event.clientX || event.pageX) + (event.pageX ? 0 : window.scrollX);
        top =
          (event.clientY || event.pageY) +
          (event.pageY ? 0 : window.scrollY) +
          10;
      } else {
        // 默认位置 - 窗口中心
        left = window.scrollX + (windowWidth - popupWidth) / 2;
        top = window.scrollY + (windowHeight - popupHeight) / 2;
      }

      // 确保弹窗不超出窗口边界
      // 水平边界检查
      if (left + popupWidth > windowWidth + window.scrollX - 20) {
        left = windowWidth + window.scrollX - popupWidth - 20;
      }
      if (left < window.scrollX + 20) {
        left = window.scrollX + 20;
      }

      // 垂直边界检查
      if (top + popupHeight > windowHeight + window.scrollY - 20) {
        // 如果下方空间不足，尝试显示在选区上方
        if (selection && selection.rangeCount > 0) {
          const range = selection.getRangeAt(0);
          const rect = range.getBoundingClientRect();
          const topSpace = rect.top;

          if (topSpace > popupHeight + 30) {
            // 上方空间足够，显示在选区上方
            top = rect.top + window.scrollY - popupHeight - 10;
          } else {
            // 上下都不够，尽量靠上显示，但保留滚动空间
            top = windowHeight + window.scrollY - popupHeight - 20;
          }
        } else {
          // 非选区情况，直接限制在窗口内
          top = windowHeight + window.scrollY - popupHeight - 20;
        }
      }
      if (top < window.scrollY + 20) {
        top = window.scrollY + 20;
      }

      return { left, top };
    }

    /**
     * 发送请求到代理服务器
     * @param {Object} data - 请求数据
     * @param {string} type - 请求类型，'translation'或'abbreviation'
     * @returns {Promise<Object>} - 服务器响应
     */
    async sendRequest(data, options = {}) {
      try {
        // 确保配置已就绪
        if (!this.configReady) {
          await this.loadServerConfig();
        }

        // 根据请求类型选择正确的配置
        const config = options.isAbbreviation
          ? this.abbrConfig
          : this.translationConfig;

        // 如果没有指定模型，使用配置中的默认模型
        if (!data.model && config.default_model) {
          console.log(
            `[请求] 使用配置的默认${
              options.isAbbreviation ? "解读" : "翻译"
            }模型:`,
            config.default_model
          );
          data.model = config.default_model;
        }

        // 构建请求URL
        const url = this.proxyUrl;

        // 添加请求类型参数，确保服务器知道这是哪种类型的请求
        const requestData = {
          ...data,
          request_type: options.isAbbreviation ? "abbreviation" : "translation", // 明确告诉服务器这是什么类型的请求
          config_type: config.type, // 添加配置类型
        };

        console.log(
          `[请求] 发送${options.isAbbreviation ? "解读" : "翻译"}请求到:`,
          url
        );
        console.log(`[请求] 使用配置:`, config);
        console.log(`[请求] 请求数据:`, requestData);

        // 发送请求
        const response = await fetch(url, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(requestData),
        });

        if (!response.ok) {
          throw new Error(`服务器响应错误: ${response.status}`);
        }

        return await response.json();
      } catch (error) {
        console.error(
          `[请求] ${options.isAbbreviation ? "解读" : "翻译"}请求失败:`,
          error
        );
        throw error;
      }
    }

    /**
     * 处理翻译请求
     */
    async handleTranslateRequest(text, targetLang) {
      try {
        // 保留现有代码...

        const result = await this.sendRequest(requestData, {
          isAbbreviation: false,
        });

        // 保留现有代码...
      } catch (error) {
        // 保留现有代码...
      }
    }

    closeHandler = (e) => {
      // 只有当点击的是关闭按钮时才关闭弹窗
      if (
        e.target.classList.contains("close-button") ||
        e.target.closest(".close-button")
      ) {
        this.hidePopup();
        document.removeEventListener("mousedown", this.closeHandler);
      }
    };

    /**
     * 解释缩写（仅使用nbnhhsh API）
     */
    explainAbbreviation() {
      // 获取选中文本
      const text = this.getSelectionText(false);
      if (!text) return;

      // 保存最后搜索的文本
      this.lastSearchedText = text;

      // 先获取工具栏位置
      let toolbarRect = null;
      if (this.toolbar) {
        const rect = this.toolbar.getBoundingClientRect();
        toolbarRect = {
          left: rect.left,
          top: rect.top,
          right: rect.right,
          bottom: rect.bottom,
          width: rect.width,
          height: rect.height,
          x: rect.x,
          y: rect.y,
        };
      }

      // 隐藏工具栏
      this.hideToolbar();

      // 清除选中的文本
      window.getSelection().removeAllRanges();

      // 显示弹窗
      this.showPopup("abbreviation", true, toolbarRect);

      // 检查缓存
      const cacheKey = `abbreviation_${text}`;
      const cachedResult = this.getFromCache(cacheKey);
      if (cachedResult) {
        this.renderContent(cachedResult, "abbreviation");
        return;
      }

      // 显示加载状态
      const content = this.popup.querySelector("#translator-explain-content");
      content.innerHTML = '<div class="translator-loading">解析缩写中...</div>';

      // 使用nbnhhsh API解释缩写
      this.fetchNbnhhshExplanation(text)
        .then((explanation) => {
          if (explanation) {
            this.addToCache(cacheKey, explanation);
            this.renderContent(explanation, "abbreviation");
          } else {
            // 如果nbnhhsh API没有结果，显示未找到结果
            content.innerHTML =
              '<div class="translator-error" style="text-align: center; padding: 20px;">未找到该缩写的解释</div>';
          }
        })
        .catch((error) => {
          console.error("nbnhhsh API出错:", error);
          content.innerHTML =
            '<div class="translator-error">解析缩写失败，请稍后再试</div>';
        });
    }

    /**
     * 从nbnhhsh API获取缩写解释
     * @param {string} text - 要解释的文本
     * @returns {Promise<Array>} - 解释结果
     */
    fetchNbnhhshExplanation(text) {
      return new Promise(async (resolve, reject) => {
        try {
          // 保存原始文本
          this.lastSearchedText = text;
          this.originalText = text;

          // 1. 提取连续2个及以上的字母数字（多字母缩写）
          const normalPattern = /[a-zA-Z0-9]{2,}/g;
          const normalMatches = text.match(normalPattern) || [];

          // 2. 提取所有"分隔符分开的单字母片段"
          const groupPattern = /([a-zA-Z0-9](?:[，,、\s]+[a-zA-Z0-9]){1,})/g;
          const groupMatches = text.match(groupPattern) || [];

          // 3. 对每个片段，生成所有长度>=2的连续组合
          function getAllCombinations(letters) {
            const result = [];
            for (let len = 2; len <= letters.length; len++) {
              for (let i = 0; i <= letters.length - len; i++) {
                result.push(letters.slice(i, i + len).join(""));
              }
            }
            return result;
          }

          let groupAbbrs = [];
          groupMatches.forEach((group) => {
            // 提取片段中的所有字母
            const letters = group.match(/[a-zA-Z0-9]/g);
            if (letters && letters.length > 1) {
              groupAbbrs.push(...getAllCombinations(letters));
            }
          });

          // 4. 合并所有缩写并去重
          let allMatches = [...normalMatches, ...groupAbbrs];
          allMatches = [...new Set(allMatches)].filter(
            (abbr) => abbr.length > 0
          );

          // 5. 如果没有任何缩写，尝试整体拼接
          if (allMatches.length === 0) {
            const cleanedText = text.replace(/[^a-zA-Z0-9]/g, "");
            if (cleanedText.length >= 2) {
              allMatches.push(cleanedText);
            }
          }

          // 6. 保存提取到的缩写列表
          this.extractedAbbreviations = allMatches;

          // 7. 构建查询文本（多个缩写用逗号分隔）
          const queryText = this.extractedAbbreviations.join(",");

          // 8. 检查缓存
          const cacheKey = `nbnhhsh_${queryText}`;
          const cachedResult = this.getFromCache(cacheKey, "guess");

          if (cachedResult) {
            resolve(cachedResult);
            return;
          }

          // 9. 发送API请求
          const apiUrl = "https://lab.magiconch.com/api/nbnhhsh/guess";
          const response = await fetch(apiUrl, {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ text: queryText }),
          });

          if (!response.ok) {
            throw new Error(`API请求失败: ${response.status}`);
          }

          const result = await response.json();

          // 10. 处理并缓存结果
          if (result && result.length > 0) {
            this.addToCache(cacheKey, result, "guess");
            resolve(result);
          } else {
            resolve(null);
          }
        } catch (error) {
          console.error("nbnhhsh API请求失败:", error);
          resolve(null);
        }
      });
    }

    /**
     * 为元素添加拖拽支持
     */
    addDragSupport(element) {
      if (!element) return;

      const headerEl = element.querySelector(".translator-header") || element;
      const dragHandle = headerEl;

      let isDragging = false;
      let offsetX, offsetY;

      // 检查元素的定位方式
      const ensureProperPositioning = () => {
        const style = window.getComputedStyle(element);
        if (style.position !== "absolute" && style.position !== "fixed") {
          element.style.position = "fixed"; // 使用fixed定位以避免滚动影响
        }
      };

      const dragStart = (e) => {
        // 仅允许鼠标左键拖拽
        if (e.button !== 0) return;

        e.preventDefault();
        e.stopPropagation();

        // 确保定位方式正确
        ensureProperPositioning();

        isDragging = true;

        // 获取鼠标在元素内的相对位置
        const rect = element.getBoundingClientRect();
        offsetX = e.clientX - rect.left;
        offsetY = e.clientY - rect.top;

        // 添加拖拽过程中使用的类
        element.classList.add("translator-dragging");

        // 在document上绑定事件，提高拖拽体验
        document.addEventListener("mousemove", drag);
        document.addEventListener("mouseup", dragEnd);
      };

      const drag = (e) => {
        if (!isDragging) return;

        // 计算新位置
        const newLeft = e.clientX - offsetX;
        const newTop = e.clientY - offsetY;

        // 根据定位方式设置位置
        if (window.getComputedStyle(element).position === "fixed") {
          // fixed定位使用视口坐标
          element.style.left = `${newLeft}px`;
          element.style.top = `${newTop}px`;
        } else {
          // absolute定位需要考虑滚动
          element.style.left = `${newLeft + window.scrollX}px`;
          element.style.top = `${newTop + window.scrollY}px`;
        }
      };

      const dragEnd = (e) => {
        if (!isDragging) return;

        isDragging = false;

        // 移除拖拽过程中使用的类
        element.classList.remove("translator-dragging");

        // 移除document上的事件监听
        document.removeEventListener("mousemove", drag);
        document.removeEventListener("mouseup", dragEnd);
      };

      // 绑定鼠标按下事件到拖拽手柄
      dragHandle.addEventListener("mousedown", dragStart);
    }

    /**
     * 统一渲染内容到弹窗
     * @param {string} content - 要渲染的内容
     * @param {string} type - 内容类型：'translation', 'explanation', 'abbreviation'
     */
    renderContent(content, type) {
      if (!this.popup) return;

      // 根据类型选择目标容器
      let container;
      if (type === "translation") {
        container = this.popup.querySelector(".translation-result");
      } else {
        container = this.popup.querySelector("#translator-explain-content");
      }

      if (!container) return;

      // 特殊处理缩写解释类型
      if (type === "abbreviation") {
        try {
          // 显示原始文本和提取的字母数字
          let html = '<div class="abbr-info">';

          // 显示原始选中文本
          html += `<div class="abbr-title">原始文本: <span class="abbr-original">"${this.escapeHtml(
            this.originalText || this.lastSearchedText || ""
          )}"</span></div>`;

          // 显示提取的缩写列表
          if (
            this.extractedAbbreviations &&
            this.extractedAbbreviations.length > 0
          ) {
            html += `<div class="abbr-title">提取的缩写: <span class="abbr-cleaned">`;
            this.extractedAbbreviations.forEach((abbr, index) => {
              html += `<span class="abbr-item">${abbr}</span>`;
              if (index < this.extractedAbbreviations.length - 1) {
                html += ", ";
              }
            });
            html += `</span></div>`;
          }

          html += "</div>";

          // 显示查询结果
          if (Array.isArray(content) && content.length > 0) {
            let hasExplanations = false;

            // 按缩写分组显示结果
            html += '<div class="abbr-results">';

            content.forEach((item) => {
              if (item.name) {
                html += `<div class="abbr-group">`;
                html += `<div class="abbr-name">${item.name}</div>`;

                if (item.trans && item.trans.length > 0) {
                  html += '<div class="abbr-meaning-list">';
                  item.trans.forEach((trans) => {
                    html += `<span class="abbr-meaning-item">${trans}</span>`;
                  });
                  html += "</div>";
                  hasExplanations = true;
                } else if (item.inputting && item.inputting.length > 0) {
                  html += '<div class="abbr-meaning-list">';
                  item.inputting.forEach((input) => {
                    html += `<span class="abbr-meaning-item abbr-inputting">${input}</span>`;
                  });
                  html += "</div>";
                  hasExplanations = true;
                } else {
                  html += '<div class="abbr-no-meaning">未找到解释</div>';
                }

                html += `</div>`;
              }
            });

            html += "</div>";

            // 如果没有找到任何解释，显示未找到提示
            if (!hasExplanations) {
              html =
                '<div class="translator-error" style="text-align: center; padding: 20px;">未找到该缩写的解释</div>';
            }
          } else {
            // 当 content 为空或不是数组时，显示未找到提示
            html +=
              '<div class="translator-error" style="text-align: center; padding: 20px;">未找到该缩写的解释</div>';
          }

          container.innerHTML = html;

          // 添加样式
          const styleId = "translator-abbr-styles";
          if (!document.getElementById(styleId)) {
            const style = document.createElement("style");
            style.id = styleId;
            style.textContent = `
              .abbr-info {
                margin-bottom: 15px;
                padding-bottom: 10px;
                border-bottom: 1px solid #eee;
              }
              .abbr-original, .abbr-cleaned {
                color: #1a73e8;
                font-weight: 500;
              }
              .abbr-title {
                margin-bottom: 5px;
                color: #555;
                font-size: 14px;
              }
              .abbr-item {
                background-color: #f1f8ff;
                padding: 2px 5px;
                border-radius: 3px;
                margin: 0 2px;
              }
              .abbr-group {
                margin-bottom: 15px;
                padding-bottom: 10px;
                border-bottom: 1px dashed #eee;
              }
              .abbr-name {
                font-weight: bold;
                font-size: 15px;
                margin-bottom: 5px;
              }
              .abbr-meaning-list {
                display: flex;
                flex-wrap: wrap;
                gap: 5px;
                margin-bottom: 5px;
              }
              .abbr-meaning-item {
                background-color: #f1f8ff;
                color: #0366d6;
                padding: 3px 8px;
                border-radius: 4px;
                display: inline-block;
                font-size: 13px;
                border: 1px solid #daebff;
              }
              .abbr-inputting {
                background-color: #fff8e1;
                color: #f57c00;
                border-color: #ffe082;
              }
              .abbr-no-meaning {
                color: #999;
                font-style: italic;
                padding: 3px 0;
              }
              .abbr-results {
                max-height: 300px;
                overflow-y: auto;
              }
            `;
            document.head.appendChild(style);
          }
        } catch (error) {
          console.error("解析缩写解释内容出错:", error);
          container.innerHTML =
            '<div class="translator-error" style="text-align: center; padding: 20px;">未找到该缩写的解释</div>';
        }
        return;
      }

      // 其他类型的内容处理（保持原有的Markdown处理逻辑）
      if (
        content.includes("#") ||
        content.includes("*") ||
        content.includes("-") ||
        content.includes("```")
      ) {
        // 使用marked.js渲染Markdown（如果可用）
        if (window.marked) {
          container.innerHTML = window.marked.parse(content);
        } else {
          // 简单的Markdown处理
          let html = content
            // 处理标题
            .replace(/^### (.*$)/gim, "<h3>$1</h3>")
            .replace(/^## (.*$)/gim, "<h2>$1</h2>")
            .replace(/^# (.*$)/gim, "<h1>$1</h1>")
            // 处理列表
            .replace(/^\* (.*$)/gim, "<ul><li>$1</li></ul>")
            .replace(/^- (.*$)/gim, "<ul><li>$1</li></ul>")
            // 处理粗体和斜体
            .replace(/\*\*(.*)\*\*/gim, "<strong>$1</strong>")
            .replace(/\*(.*)\*/gim, "<em>$1</em>")
            // 处理代码块
            .replace(/```([\s\S]*?)```/gm, "<pre><code>$1</code></pre>")
            // 处理换行
            .replace(/\n/gim, "<br>");

          // 合并连续的ul标签
          html = html.replace(/<\/ul><ul>/g, "");

          container.innerHTML = html;
        }
      } else {
        // 纯文本，直接显示，但保留换行
        container.innerHTML = content.replace(/\n/g, "<br>");
      }

      // 处理敏感内容标记
      const sensitiveSpans = container.querySelectorAll(".sensitive-content");
      sensitiveSpans.forEach((span) => {
        // 已在CSS中设置样式，无需在此处添加内联样式
      });

      // 处理链接
      const links = container.querySelectorAll("a");
      links.forEach((link) => {
        if (!link.hasAttribute("target")) {
          link.target = "_blank"; // 在新标签页打开链接
        }
      });
    }

    // 统一API调用接口
    async callApi(endpoint, data, options = {}) {
      // 统一的API调用实现
    }
  }

  // 创建新实例并保存到全局
  window.TextTranslator = TextTranslator;
  window.translatorInstance = new TextTranslator();
})();
