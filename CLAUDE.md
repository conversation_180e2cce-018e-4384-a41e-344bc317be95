# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

GLM-Highlight is a Chrome browser extension (Manifest V3) that provides powerful text highlighting functionality with multi-category support, color customization, configuration import/export, and sharing features. The extension allows users to highlight text on web pages with different colors and categories.

## Architecture

### Core Components

- **Background Script** (`js/background.js`): Service worker handling storage, messaging, domain rules, and tab management
- **Content Scripts**: Injected into web pages to handle highlighting functionality
  - `js/highlighter.js`: Core highlighting engine with performance optimizations
  - `js/content-action.js`: DOM observation and user interaction handling
  - `js/config.js`: Configuration management and filtering rules
  - `js/utils.js`: Utility functions for error handling, debouncing, and DOM operations
- **UI Components**:
  - `popup.html/js`: Extension popup interface
  - `options.html/js`: Settings and configuration page
  - `manage.html/js`: Keyword management interface

### Key Features

1. **Multi-category Highlighting**: Support for multiple highlight categories with different colors
2. **Performance Optimization**: 
   - Time-sliced processing for large documents
   - Viewport-priority rendering
   - LRU caching for patterns and nodes
   - Batch DOM updates
3. **Domain Management**: Whitelist/blacklist functionality for controlling which sites allow highlighting
4. **State Management**: Per-tab and per-URL state persistence
5. **Import/Export**: Configuration sharing via JSON or share codes

## Development Commands

This is a Chrome extension project without traditional build tools. Development workflow:

1. **Load Extension**: 
   - Open Chrome → Extensions → Developer mode → "Load unpacked"
   - Select the project directory

2. **Testing**: 
   - Manual testing in browser
   - Check console for errors in background script and content scripts
   - Use Chrome DevTools for debugging

3. **Reload Extension**: 
   - After code changes, click reload button in Chrome Extensions page
   - Content scripts require page refresh to update

## Code Architecture Details

### Highlighting Engine (`js/highlighter.js`)

The `TextHighlighter` class is the core engine with these key features:

- **Performance Optimizations**:
  - Time-sliced processing (`processBatch()`) to avoid blocking main thread
  - LRU pattern cache for regex compilation
  - Viewport-priority processing
  - KMP algorithm for efficient string matching in long texts

- **Configuration System**: Uses `HighlighterConfig` from `config.js` for:
  - Filter rules for skipping certain DOM elements
  - Performance settings (batch sizes, timeouts)
  - CSS class names and styling

### State Management (`js/background.js`)

- **Storage Strategy**: 
  - `chrome.storage.local` for keywords and tab-specific states
  - `chrome.storage.sync` for domain rules (synced across devices)
  - In-memory caching with `CacheManager` class

- **Tab State Hierarchy**:
  1. URL-specific state (highest priority)
  2. Global extension state (fallback)
  3. Domain rules (can override both)

### Message Passing

Uses Chrome extension messaging API with RPC pattern:
```javascript
chrome.runtime.sendMessage({
  opt: "rpc",
  func: "functionName", 
  args: [arg1, arg2]
})
```

### DOM Observation (`js/content-action.js`)

- Uses `MutationObserver` to detect DOM changes
- Throttled processing to handle dynamic content
- Viewport intersection detection for performance

## File Structure Notes

- `css/`: Stylesheets for highlighting and UI components
- `img/`: Extension icons and UI assets  
- `server/`: PHP backend files for sharing functionality (optional)
- `manifest.json`: Extension configuration (permissions, content scripts, etc.)

## Performance Considerations

- The extension is optimized for large documents with thousands of highlights
- Uses time-slicing to prevent UI blocking
- Implements efficient caching strategies
- Prioritizes viewport content for better user experience

## Security Notes

- Content Security Policy configured in manifest
- Input sanitization in popup and options pages
- Domain validation for highlight rules
- No eval() or unsafe DOM manipulation

## Common Development Tasks

- **Adding new highlight categories**: Modify keyword management in `manage.js`
- **Performance tuning**: Adjust batch sizes and timeouts in `config.js`
- **UI changes**: Update corresponding HTML/CSS and JavaScript files
- **Storage schema changes**: Update both background and content script handlers