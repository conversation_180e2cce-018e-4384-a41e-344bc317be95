// 统一的缓存管理器
class CacheManager {
  constructor(options = {}) {
    this.cache = new Map();
    this.ttl = options.ttl || 5 * 60 * 1000; // 5分钟
    this.maxAge = options.maxAge || 30 * 60 * 1000; // 30分钟
    this.maxSize = options.maxSize || 1000;
    this.cleanupInterval = options.cleanupInterval || 2 * 60 * 1000;

    // 启动定时清理
    this.cleanupTimer = setInterval(() => this.cleanup(), this.cleanupInterval);
  }

  set(key, value) {
    // 检查缓存大小
    if (this.cache.size >= this.maxSize) {
      const oldestKey = Array.from(this.cache.keys()).sort(
        (a, b) => this.cache.get(a).timestamp - this.cache.get(b).timestamp
      )[0];
      this.cache.delete(oldestKey);
    }

    this.cache.set(key, {
      value,
      timestamp: Date.now(),
      lastAccessed: Date.now(),
    });
  }

  get(key) {
    const item = this.cache.get(key);
    if (!item) return null;

    const now = Date.now();
    if (now - item.timestamp > this.maxAge) {
      this.cache.delete(key);
      return null;
    }

    // 更新访问时间
    item.lastAccessed = now;
    return item.value;
  }

  cleanup() {
    const now = Date.now();
    for (const [key, item] of this.cache) {
      if (
        now - item.timestamp > this.maxAge ||
        now - item.lastAccessed > this.ttl
      ) {
        this.cache.delete(key);
      }
    }
  }

  clear() {
    this.cache.clear();
  }

  destroy() {
    clearInterval(this.cleanupTimer);
    this.clear();
  }
}

const background = {
  USER_ID_STORE: "userUUID",
  ACTIVE_STATUS_STORE: "isActive",
  KEYWORDS_STRING_STORE: "fwm_keywordsString",
  KEYWORDS_ARRAY_STORE: "fwm_keywordsArray",
  TAB_ACTIVE_STATUS: "tabActiveStatus",
  DOMAIN_RULES_KEY: "domain_rules",
  TAB_CATEGORY_STATUS: "tabCategoryStatus",
  TRANSLATOR_TOOLBAR_KEY: "translator_toolbar_enabled",
  URL_ACTIVE_STATUS: "urlActiveStatus",

  // 使用新的缓存管理器
  _cache: new CacheManager({
    ttl: 5 * 60 * 1000,
    maxSize: 1000,
    cleanupInterval: 2 * 60 * 1000,
  }),

  tabActiveStatus: new Map(),
  tabCategoryStatus: new Map(),
  domainRules: null,
  appliedDomainRules: null,

  async setLocalStorage(key, value) {
    try {
      const oldValue = await this.getLocalStorage(key);
      if (oldValue === value) return;

      await chrome.storage.local.set({ [key]: value });
      this._cache.set(key, value);

      await this._broadcastChange(key, value);
    } catch (error) {
      Utils.handleError(error, "setLocalStorage");
    }
  },

  async getLocalStorage(key) {
    try {
      const cached = this._cache.get(key);
      if (cached !== null) return cached;

      const result = await chrome.storage.local.get(key);
      this._cache.set(key, result[key]);
      return result[key];
    } catch (error) {
      Utils.handleError(error, "getLocalStorage");
      return null;
    }
  },

  async _broadcastChange(key, value) {
    try {
      const [activeTab] = await chrome.tabs.query({
        active: true,
        currentWindow: true,
        url: ["http://*/*", "https://*/*"],
      });

      if (activeTab?.id) {
        await chrome.tabs
          .sendMessage(activeTab.id, {
            opt: "event",
            event: "storageChange",
            args: { key, value },
          })
          .catch(error => {
            Utils.handleError(error, 'broadcastChange', 'NETWORK');
          });
      }
    } catch (error) {
      Utils.handleError(error, "broadcastChange");
    }
  },

  // 统一的消息验证
  _verifyMessage(message) {
    if (!message || typeof message !== "object") return false;
    if (!message.opt || !message.func) return false;
    if (message.opt !== "rpc") return false;
    if (typeof this[message.func] !== "function") return false;
    return true;
  },

  async setKeywordsString2(keywords, options = {}) {
    try {
      // 处理关键词,确保同一个词只出现一次
      const wordMap = new Map();

      // 提前获取当前存储的关键词列表
      let currentList = null;
      if (options.isTabSpecific) {
        currentList = await this.getLocalStorage(this.KEYWORDS_STRING_STORE);
      }

      const processedKeywords = keywords.reduce((acc, item, idx) => {
        // 获取之前保存的关键词列表，保留原来的status
        let status = item.status;

        // 重要：如果是标签页特定操作，不保存分类开关状态到全局
        if (
          options.isTabSpecific &&
          currentList &&
          Array.isArray(currentList) &&
          currentList[idx]
        ) {
          // 保留原状态，不更新全局分类状态
          status = currentList[idx].status;
        }

        if (item.data) {
          const words = item.data.trim().split(/\s+/).filter(Boolean);
          words.forEach((word) => {
            wordMap.set(word, {
              colour: item.colour,
              words: word,
              categoryIndex: idx,
            });
          });
        }
        return acc;
      }, []);

      // 将 Map 转换为数组,并按照 categoryIndex 排序
      const uniqueKeywords = Array.from(wordMap.values()).sort(
        (a, b) => a.categoryIndex - b.categoryIndex
      );

      // 确保存储的是字符串格式
      const dataToStore =
        typeof keywords === "string" ? keywords : JSON.stringify(keywords);

      // 如果是标签页特定操作，需要保留原有分类开关状态
      if (options.isTabSpecific && currentList && Array.isArray(currentList)) {
        // 创建修改后的列表，但保留原来的status
        const updatedList = keywords.map((item, idx) => {
          if (idx < currentList.length) {
            // 保留原状态，只更新内容和颜色
            return {
              ...item,
              status: currentList[idx].status,
            };
          }
          return item;
        });

        // 保存修改后的数据
        await this.setLocalStorage(this.KEYWORDS_STRING_STORE, updatedList);

        // 警告：不要直接使用uniqueKeywords，因为它已经丢失了分类信息
        // 需要重新处理一遍，确保分类信息正确
        const wordMapWithStatus = new Map();
        updatedList.forEach((item, idx) => {
          if (item.status === 1 && item.data) {
            const words = item.data.trim().split(/\s+/).filter(Boolean);
            words.forEach((word) => {
              wordMapWithStatus.set(word, {
                colour: item.colour,
                words: word,
                categoryIndex: idx,
              });
            });
          }
        });

        // 重新排序
        const filteredKeywords = Array.from(wordMapWithStatus.values()).sort(
          (a, b) => a.categoryIndex - b.categoryIndex
        );

        await this.setKeywords(filteredKeywords);
        return filteredKeywords;
      }

      // 默认行为：正常保存数据
      await this.setLocalStorage(this.KEYWORDS_STRING_STORE, dataToStore);
      await this.setKeywords(uniqueKeywords);

      return uniqueKeywords;
    } catch (error) {
      console.error("设置关键词字符串失败:", error);
      return null;
    }
  },

  async getKeywords() {
    return await this.getLocalStorage(this.KEYWORDS_ARRAY_STORE);
  },

  async setKeywords(keywords) {
    try {
      await this.setLocalStorage(this.KEYWORDS_ARRAY_STORE, keywords);
    } catch (error) {
      console.error("设置关键词失败:", error);
    }
  },

  async setTabActiveStatus(tabId, status) {
    try {
      // 只更新特定标签页状态，不修改全局状态
      this.tabActiveStatus.set(tabId, status);
      // 使用storage.local而不是localStorage，更高效且独立
      await chrome.storage.local.set({
        [`${this.TAB_ACTIVE_STATUS}_${tabId}`]: status,
      });

      // 不再需要调用setLocalStorage，以避免更新全局状态
    } catch (error) {
      console.error("设置标签页状态失败:", error);
    }
  },

  async getTabActiveStatus(tabId) {
    try {
      // 获取标签页信息
      const tab = await chrome.tabs.get(tabId).catch(() => null);
      if (!tab || !tab.url) {
        return true; // 默认开启
      }

      // 检查域名规则
      const shouldHighlight = await this.shouldHighlightDomain(tab.url);
      if (shouldHighlight === false) {
        return false; // 如果域名在黑名单中，禁用高亮
      }

      const normalizedUrl = this.normalizeUrl(tab.url);

      // 优先从内存缓存获取URL状态
      if (this.tabActiveStatus.has(normalizedUrl)) {
        const status = this.tabActiveStatus.get(normalizedUrl);
        console.log(`从内存缓存获取URL状态: ${normalizedUrl} = ${status}`);
        return status !== false;
      }

      // 从存储获取URL状态（支持旧键迁移）
      const key = `${this.URL_ACTIVE_STATUS}_${normalizedUrl}`;
      const legacyKey = `undefined_${normalizedUrl}`;
      const result = await chrome.storage.local.get([key, legacyKey]);
      let urlStatus = result[key];
      if (urlStatus === undefined && result[legacyKey] !== undefined) {
        urlStatus = result[legacyKey];
        // 迁移到新键并删除旧键
        await chrome.storage.local.set({ [key]: urlStatus });
        await chrome.storage.local.remove(legacyKey);
      }

      if (urlStatus !== null && urlStatus !== undefined) {
        // 缓存到内存
        this.tabActiveStatus.set(normalizedUrl, urlStatus);
        console.log(`从存储恢复URL状态: ${normalizedUrl} = ${urlStatus}`);
        return urlStatus !== false;
      }

      // 如果没有找到URL状态，使用全局状态
      const globalStatus = await this.getActiveStatus();
      this.tabActiveStatus.set(normalizedUrl, globalStatus);
      console.log(`使用全局状态: ${normalizedUrl} = ${globalStatus}`);
      return globalStatus;
    } catch (error) {
      console.error("获取标签页状态失败:", error);
      return true; // 错误情况下默认开启
    }
  },

  normalizeUrl(url) {
    try {
      // 标准化URL：保留协议、域名、路径；若为SPA hash路由(以"#/"开头)则包含hash
      const urlObj = new URL(url);
      const base = `${urlObj.protocol}//${urlObj.host}${urlObj.pathname}`;
      const h = urlObj.hash;
      const normalized = h && h.startsWith("#/") ? `${base}${h}` : base;
      console.log(`URL标准化: ${url} → ${normalized}`);
      return normalized;
    } catch (error) {
      console.error("URL标准化失败:", error);
      return url; // 如果解析失败，返回原始URL
    }
  },

  generateUUID() {
    const lut = Array(256)
      .fill()
      .map((_, i) => (i < 16 ? "0" : "") + i.toString(16));
    return () => {
      const d0 = (Math.random() * 0xffffffff) | 0;
      const d1 = (Math.random() * 0xffffffff) | 0;
      const d2 = (Math.random() * 0xffffffff) | 0;
      const d3 = (Math.random() * 0xffffffff) | 0;
      return `${
        lut[d0 & 0xff] +
        lut[(d0 >> 8) & 0xff] +
        lut[(d0 >> 16) & 0xff] +
        lut[(d0 >> 24) & 0xff]
      }-${lut[d1 & 0xff]}${lut[(d1 >> 8) & 0xff]}-${
        lut[((d1 >> 16) & 0x0f) | 0x40]
      }${lut[(d1 >> 24) & 0xff]}-${lut[(d2 & 0x3f) | 0x80]}${
        lut[(d2 >> 8) & 0xff]
      }-${lut[(d2 >> 16) & 0xff]}${lut[(d2 >> 24) & 0xff]}${lut[d3 & 0xff]}${
        lut[(d3 >> 8) & 0xff]
      }${lut[(d3 >> 16) & 0xff]}${lut[(d3 >> 24) & 0xff]}`;
    };
  },

  async getUserId() {
    try {
      let userUUID = await this.getLocalStorage(this.USER_ID_STORE);
      if (!userUUID) {
        userUUID = this.generateUUID();
        await this.setLocalStorage(this.USER_ID_STORE, userUUID);
      }
      return userUUID;
    } catch (error) {
      console.error("获取用户ID失败:", error);
      return null;
    }
  },

  async getActiveStatus() {
    try {
      // 获取全局总开关状态
      // 这是所有URL的默认状态，当没有URL特定状态时使用
      const globalStatus = await this.getLocalStorage(this.ACTIVE_STATUS_STORE);

      // 如果没有设置过全局状态，默认为true
      if (globalStatus === null || globalStatus === undefined) {
        return true;
      }

      return globalStatus !== false;
    } catch (error) {
      console.error("获取激活状态失败:", error);
      return true; // 出错默认开启高亮
    }
  },

  async setActiveStatus(status) {
    try {
      console.log(`setActiveStatus调用 - 设置状态为: ${status}`);

      // 设置全局总开关状态
      const newStatus = Boolean(status);
      await this.setLocalStorage(this.ACTIVE_STATUS_STORE, newStatus);
      console.log(`全局状态已保存: ${newStatus}`);

      // 获取当前活动标签页，通知更新
      const [activeTab] = await chrome.tabs.query({
        active: true,
        currentWindow: true,
        url: ["http://*/*", "https://*/*"],
      });

      if (activeTab) {
        console.log(`当前活动标签页: ${activeTab.id}, URL: ${activeTab.url}`);

        const shouldHighlight = await this.shouldHighlightDomain(activeTab.url);
        console.log(`域名高亮检查结果: ${shouldHighlight}`);

        // 读取当前URL是否已存在页面级状态
        let tabStatus;
        try {
          const normalizedUrl = this.normalizeUrl(activeTab.url);
          if (this.tabActiveStatus.has(normalizedUrl)) {
            tabStatus = this.tabActiveStatus.get(normalizedUrl);
          } else {
            const key = `${this.URL_ACTIVE_STATUS}_${normalizedUrl}`;
            const legacyKey = `undefined_${normalizedUrl}`;
            const result = await chrome.storage.local.get([key, legacyKey]);
            tabStatus = result[key] ?? result[legacyKey];
          }
        } catch (e) {}

        // 通知当前标签页更新状态：若有页面级状态则优先使用它，否则发全局状态
        try {
          const payload = {
            opt: "event",
            event: "updateHighlightStatus",
            shouldHighlight,
          };
          if (tabStatus !== undefined) {
            payload.tabStatus = tabStatus;
          } else {
            payload.globalActive = newStatus; // 无页面级状态才广播全局值
          }
          console.log("发送状态更新消息:", payload);
          // 发送前再次校验tab是否仍然存在，避免控制台噪声
          try {
            const alive = await chrome.tabs.get(activeTab.id).catch(() => null);
            if (!alive) return;
          } catch (_) {}
          await chrome.tabs.sendMessage(activeTab.id, payload).catch((err) => {
            const msg = String(err || "");
            if (/No tab with id/i.test(msg)) return; // 静默已知噪声
            console.warn("sendMessage失败:", msg);
          });
        } catch (err) {
          console.log("无法发送消息到标签页", activeTab.id);
        }
      }
    } catch (error) {
      console.error("设置激活状态失败:", error);
    }
  },

  async setUrlActiveStatus(status) {
    try {
      // 只获取当前活动标签页
      const [activeTab] = await chrome.tabs.query({
        active: true,
        currentWindow: true,
        url: ["http://*/*", "https://*/*"],
      });

      if (activeTab) {
        console.log(`setUrlActiveStatus调用 - 设置状态为: ${status}`);
        console.log(`当前活动标签页: ${activeTab.id}, URL: ${activeTab.url}`);

        // 检查活动标签页是否在黑名单中
        const shouldHighlight = await this.shouldHighlightDomain(activeTab.url);
        console.log(`域名高亮检查结果: ${shouldHighlight}`);

        // 直接使用用户设置的状态，不受域名规则影响
        // 用户手动操作应该优先于域名规则
        const newStatus = Boolean(status);

        // 只保存URL状态
        if (activeTab.url) {
          const normalizedUrl = this.normalizeUrl(activeTab.url);

          // 更新内存缓存（使用URL作为键）
          this.tabActiveStatus.set(normalizedUrl, newStatus);

          // 保存URL状态到存储
          await chrome.storage.local.set({
            [`${this.URL_ACTIVE_STATUS}_${normalizedUrl}`]: newStatus,
          });
          // 清理旧键
          await chrome.storage.local.remove(`undefined_${normalizedUrl}`);
          console.log(
            `URL状态已保存 - URL: ${normalizedUrl}, 状态: ${newStatus}`
          );
        }

        // 通知当前标签页更新状态
        try {
          console.log(
            `发送状态更新消息 - tabStatus: ${newStatus}, shouldHighlight: ${shouldHighlight}`
          );
          // 发送前再次校验tab是否仍然存在
          try {
            const alive = await chrome.tabs.get(activeTab.id).catch(() => null);
            if (!alive) return;
          } catch (_) {}
          await chrome.tabs
            .sendMessage(activeTab.id, {
              opt: "event",
              event: "updateHighlightStatus",
              shouldHighlight: true, // 用户手动操作时，强制允许高亮，不受域名规则限制
              tabStatus: newStatus,
            })
            .catch((err) => {
              const msg = String(err || "");
              if (/No tab with id/i.test(msg)) return;
              console.warn("sendMessage失败:", msg);
            });
        } catch (err) {
          // 忽略不可发送消息的错误
          console.log("无法发送消息到标签页", activeTab.id);
        }
      }
    } catch (error) {
      console.error("设置URL激活状态失败:", error);
    }
  },

  // 并行处理，减少延迟
  async setUrlActiveStatusOptimized(status) {
    try {
      const startTime = performance.now();

      // 步骤1：获取当前活动标签页（必须串行）
      const [activeTab] = await chrome.tabs.query({
        active: true,
        currentWindow: true,
        url: ["http://*/*", "https://*/*"],
      });

      if (!activeTab) {
        console.warn("未找到活动标签页");
        return;
      }

      console.log(
        `setUrlActiveStatusOptimized - 状态: ${status}, 标签页: ${activeTab.id}`
      );

      const newStatus = Boolean(status);
      const normalizedUrl = this.normalizeUrl(activeTab.url);

      // 步骤2：并行执行存储和消息发送
      const updateOperations = [];

      // 2.1 更新内存缓存（同步操作）
      this.tabActiveStatus.set(normalizedUrl, newStatus);

      // 2.2 存储操作
      const storageOp = chrome.storage.local
        .set({
          [`${this.URL_ACTIVE_STATUS}_${normalizedUrl}`]: newStatus,
        })
        .then(() => {
          console.log(`存储完成 - URL: ${normalizedUrl}, 状态: ${newStatus}`);
        });
      updateOperations.push(storageOp);

      // 2.3 清理旧键（低优先级，异步执行）
      const cleanupOp = chrome.storage.local.remove(
        `undefined_${normalizedUrl}`
      );
      updateOperations.push(cleanupOp);

      // 2.4 发送消息到内容脚本
      const messageOp = (async () => {
        try {
          // 快速检查标签页是否存在
          const alive = await chrome.tabs.get(activeTab.id).catch(() => null);
          if (!alive) return;

          await chrome.tabs.sendMessage(activeTab.id, {
            opt: "event",
            event: "updateHighlightStatus",
            shouldHighlight: true, // 用户操作优先，不受域名规则限制
            tabStatus: newStatus,
          });

          console.log(`消息发送完成 - 标签页: ${activeTab.id}`);
        } catch (err) {
          const msg = String(err || "");
          if (!/No tab with id/i.test(msg)) {
            console.warn("消息发送失败:", msg);
          }
        }
      })();
      updateOperations.push(messageOp);

      // 并行等待所有操作完成
      await Promise.all(updateOperations);

      const endTime = performance.now();
      console.log(
        `URL状态更新完成 - 总耗时: ${(endTime - startTime).toFixed(2)}ms`
      );
    } catch (error) {
      console.error("URL状态设置失败:", error);
      throw error; // 重新抛出错误，让调用者处理
    }
  },

  async getKeywordsString() {
    try {
      return (await this.getLocalStorage(this.KEYWORDS_STRING_STORE)) || "";
    } catch (error) {
      console.error("获取关键词字符串失败:", error);
      return "";
    }
  },

  async getKeywordsString2() {
    try {
      const data = await this.getLocalStorage(this.KEYWORDS_STRING_STORE);
      if (!data) return [];

      // 如果已经是对象就直接返回,否则尝试解析
      return typeof data === "object" ? data : JSON.parse(data);
    } catch (error) {
      console.error("获取关键词字符串2失败:", error);
      return [];
    }
  },

  async reapplyHighlights() {
    try {
      const [activeTab] = await chrome.tabs.query({
        active: true,
        currentWindow: true,
        url: ["http://*/*", "https://*/*"],
      });

      if (activeTab) {
        await chrome.tabs
          .sendMessage(activeTab.id, {
            opt: "event",
            event: "reapplyHighlights",
          })
          .catch(error => {
            Utils.handleError(error, 'reapplyHighlights', 'NETWORK');
          });
      }
    } catch (error) {
      console.warn("Reapply highlights failed:", error);
    }
  },

  // 优化消息处理的批处理
  async processBatchMessages(messages) {
    const batchSize = this.config.performance.calculateBatchSize();
    for (let i = 0; i < messages.length; i += batchSize) {
      const batch = messages.slice(i, i + batchSize);
      await Promise.all(batch.map((msg) => this.processMessage(msg)));

      // 使用配置的延迟
      await new Promise((resolve) =>
        setTimeout(resolve, this.config.performance.processDelay)
      );
    }
  },

  async getDomainRules() {
    if (this.domainRules) {
      return this.domainRules;
    }

    try {
      const result = await chrome.storage.sync.get(this.DOMAIN_RULES_KEY);
      const rules = result[this.DOMAIN_RULES_KEY];
      if (rules) {
        this.domainRules = rules;
        // 同时初始化应用规则缓存
        this.appliedDomainRules = JSON.parse(JSON.stringify(rules));
      } else {
        // 修改默认规则 - 黑名单模式且默认为空，意味着默认情况下所有网站都允许高亮
        this.domainRules = {
          mode: "blacklist",
          domains: [],
        };
        // 初始化应用规则缓存
        this.appliedDomainRules = JSON.parse(JSON.stringify(this.domainRules));
        // 保存默认规则到存储
        await chrome.storage.sync.set({
          [this.DOMAIN_RULES_KEY]: this.domainRules,
        });
      }
      return this.domainRules;
    } catch (error) {
      console.error("获取域名规则失败:", error);
      return {
        mode: "blacklist",
        domains: [],
      };
    }
  },

  async shouldHighlightDomain(url) {
    try {
      // 如果没有URL，默认高亮
      if (!url) return true;

      // 解析域名
      let domain;
      try {
        domain = new URL(url).hostname;
      } catch (e) {
        console.error("URL解析错误:", e);
        return true; // URL解析失败默认允许高亮
      }

      if (!domain) return true;

      // 重要修改：使用已应用的规则缓存，而不是最新规则
      // 如果应用规则缓存不存在，初始化它
      if (!this.appliedDomainRules) {
        // 获取最新规则并用它初始化应用规则缓存
        await this.getDomainRules();
        this.appliedDomainRules = JSON.parse(JSON.stringify(this.domainRules));
      }

      // 使用应用规则缓存
      const rules = this.appliedDomainRules;

      // 检查域名/URL是否在列表中
      const isDomainInList = rules.domains.some((d) => {
        // 如果规则包含'/'，则视为URL规则
        if (d.includes("/")) {
          // 简单检查：当前URL是否包含规则字符串
          return url.includes(d);
        }
        // 否则，视为域名规则
        // 精确匹配
        if (domain === d) return true;
        // 子域名匹配 (如果 d 是 example.com，domain 是 sub.example.com 也匹配)
        if (domain.endsWith("." + d)) return true;
        return false;
      });

      // 根据模式判断
      if (rules.mode === "whitelist") {
        // 白名单模式：域名在列表中才高亮
        return isDomainInList;
      } else {
        // 黑名单模式：域名不在列表中才高亮
        return !isDomainInList;
      }
    } catch (error) {
      console.error("域名检查失败:", error);
      return true; // 出错默认高亮
    }
  },

  async handleDomainRulesUpdate(rules) {
    try {
      // 只更新内存中的规则，但不修改应用的缓存规则
      this.domainRules = rules;

      // 记录规则已更改，提示用户需要刷新页面才会生效
      console.log("域名规则已更新，但只有在页面刷新后才会生效");

      // 不自动更新应用的缓存规则，确保不会立即生效
      // 规则只会在标签页刷新/重新加载时更新（在onUpdated事件处理中）

      // 存储最新的规则到存储，但不应用
      await chrome.storage.sync.set({
        [this.DOMAIN_RULES_KEY]: rules,
      });
    } catch (error) {
      console.error("处理域名规则更新失败:", error);
    }
  },

  async updateTabHighlightStatus(tabId, url, isUserAction = false) {
    try {
      // 使用已应用的缓存规则检查域名，而不是最新规则
      // 确保黑/白名单设置只在刷新后生效
      const shouldHighlight = await this.shouldHighlightDomain(url);

      // 获取URL状态（简化逻辑，只基于URL）
      const normalizedUrl = this.normalizeUrl(url);
      let urlStatus = null;

      // 优先从内存缓存获取URL状态
      if (this.tabActiveStatus.has(normalizedUrl)) {
        urlStatus = this.tabActiveStatus.get(normalizedUrl);
      } else {
        // 从存储获取URL状态
        urlStatus = await chrome.storage.local
          .get(`${this.URL_ACTIVE_STATUS}_${normalizedUrl}`)
          .then(
            (result) => result[`${this.URL_ACTIVE_STATUS}_${normalizedUrl}`]
          );

        // 缓存到内存
        if (urlStatus !== null && urlStatus !== undefined) {
          this.tabActiveStatus.set(normalizedUrl, urlStatus);
          console.log(
            `从URL状态继承 - 标签页 ${tabId}, URL: ${normalizedUrl}, 状态: ${urlStatus}`
          );
        }
      }

      // 如果是用户手动操作，不要覆盖已有的状态
      if (isUserAction && urlStatus !== null && urlStatus !== undefined) {
        console.log(
          `跳过自动更新 - 标签页 ${tabId} 已有用户设置的状态: ${urlStatus}`
        );
        return;
      }

      // 重要：如果没有特定状态，获取默认的全局状态
      let finalStatus;
      if (urlStatus !== null && urlStatus !== undefined) {
        finalStatus = urlStatus;
        console.log(`使用URL状态: ${finalStatus}`);
      } else {
        finalStatus = await this.getActiveStatus();
        console.log(`使用全局状态: ${finalStatus}`);
      }

      console.log(
        `自动更新标签页状态 - tabId: ${tabId}, finalStatus: ${finalStatus}, shouldHighlight: ${shouldHighlight}`
      );

      // 通知内容脚本更新高亮状态
      try {
        await chrome.tabs.sendMessage(tabId, {
          opt: "event",
          event: "updateHighlightStatus",
          shouldHighlight: shouldHighlight,
          tabStatus: finalStatus,
        });
      } catch (err) {
        // 忽略不可发送消息的错误（例如未加载的页面）
        console.log("无法发送消息到标签页", tabId, err);
      }
    } catch (error) {
      console.error("更新标签页高亮状态失败:", error);
    }
  },

  // 新增：广播消息到所有活动标签页
  // 此函数已移除以避免不必要的同步

  // 添加：更新应用的规则缓存（在页面刷新时调用）
  async updateAppliedRules() {
    try {
      // 确保domainRules已经是最新的
      if (!this.domainRules) {
        await this.getDomainRules();
      }

      // 将最新编辑的规则复制到应用规则缓存
      // 使用深拷贝确保两个对象完全独立
      this.appliedDomainRules = JSON.parse(JSON.stringify(this.domainRules));
      console.log("已更新应用的域名规则缓存", {
        mode: this.appliedDomainRules.mode,
        domainsCount: this.appliedDomainRules.domains.length,
      });

      return this.appliedDomainRules;
    } catch (error) {
      console.error("更新应用规则缓存失败:", error);
      // 如果更新失败，确保appliedDomainRules至少有一个有效值
      if (!this.appliedDomainRules) {
        this.appliedDomainRules = {
          mode: "blacklist",
          domains: [],
        };
      }
      return this.appliedDomainRules;
    }
  },

  // 添加获取标签页分类状态的方法
  async getTabCategoryStatus(tabId, categoryIndex) {
    try {
      // 如果存在缓存，优先使用缓存
      const cacheKey = `${tabId}_${categoryIndex}`;
      if (this.tabCategoryStatus.has(cacheKey)) {
        return this.tabCategoryStatus.get(cacheKey);
      }

      // 否则从存储中获取
      const storageKey = `${this.TAB_CATEGORY_STATUS}_${tabId}_${categoryIndex}`;
      const result = await chrome.storage.local.get(storageKey);
      const status = result[storageKey];

      // 如果存储中不存在，返回 null（表示使用全局状态）
      if (status === undefined) {
        return null;
      }

      // 更新缓存
      this.tabCategoryStatus.set(cacheKey, status);
      return status;
    } catch (error) {
      console.error("获取标签页分类状态失败:", error);
      return null; // 出错时返回 null（使用全局状态）
    }
  },

  // 添加设置标签页分类状态的方法
  async setTabCategoryStatus(tabId, categoryIndex, status) {
    try {
      // 更新缓存
      const cacheKey = `${tabId}_${categoryIndex}`;
      this.tabCategoryStatus.set(cacheKey, status);

      // 更新存储
      const storageKey = `${this.TAB_CATEGORY_STATUS}_${tabId}_${categoryIndex}`;
      await chrome.storage.local.set({ [storageKey]: status });

      // 记录日志
      console.log(
        `设置标签页 ${tabId} 的分类 ${categoryIndex} 状态为: ${
          status ? "启用" : "禁用"
        }`
      );

      return true;
    } catch (error) {
      console.error("设置标签页分类状态失败:", error);
      return false;
    }
  },

  // 添加获取当前标签页ID的方法
  async getCurrentTabId() {
    try {
      const [tab] = await chrome.tabs.query({
        active: true,
        currentWindow: true,
      });
      return tab?.id;
    } catch (error) {
      console.error("获取当前标签页ID失败:", error);
      return null;
    }
  },

  // 获取URL特定的激活状态
  async getUrlActiveStatus(url) {
    try {
      if (!url) return null;
      
      const normalizedUrl = this.normalizeUrl(url);
      
      // 优先从内存缓存获取
      if (this.tabActiveStatus.has(normalizedUrl)) {
        const status = this.tabActiveStatus.get(normalizedUrl);
        console.log(`从内存获取URL状态: ${normalizedUrl} = ${status}`);
        return status;
      }
      
      // 从存储获取URL状态
      const key = `${this.URL_ACTIVE_STATUS}_${normalizedUrl}`;
      const result = await chrome.storage.local.get(key);
      const urlStatus = result[key];
      
      // 缓存到内存（如果存在）
      if (urlStatus !== null && urlStatus !== undefined) {
        this.tabActiveStatus.set(normalizedUrl, urlStatus);
        console.log(`从存储获取URL状态: ${normalizedUrl} = ${urlStatus}`);
        return urlStatus;
      }
      
      console.log(`URL无特定状态: ${normalizedUrl}`);
      return null; // 没有URL特定状态
      
    } catch (error) {
      Utils.handleError(error, 'getUrlActiveStatus', 'STORAGE');
      return null;
    }
  },

  // 批处理消息处理
  async processBatchMessages(messages) {
    const results = [];
    const errors = [];

    for (const message of messages) {
      try {
        // 移除批处理相关的元数据
        const { _timestamp, _priority, ...cleanMessage } = message;
        
        let result;
        
        // 处理RPC消息
        if (cleanMessage.opt === 'rpc') {
          if (!this._verifyMessage(cleanMessage)) {
            throw new Error('无效的RPC消息格式');
          }
          
          const func = this[cleanMessage.func];
          if (typeof func === 'function') {
            result = await func.apply(this, cleanMessage.args || []);
          } else {
            throw new Error(`未找到函数: ${cleanMessage.func}`);
          }
        }
        // 处理事件消息
        else if (cleanMessage.opt === 'event') {
          switch (cleanMessage.event) {
            case 'domainRulesUpdated':
              this.handleDomainRulesUpdate(cleanMessage.data);
              result = { success: true };
              break;
            default:
              throw new Error(`未处理的事件类型: ${cleanMessage.event}`);
          }
        }
        else {
          throw new Error(`未知的消息类型: ${cleanMessage.opt}`);
        }

        results.push({
          success: true,
          result,
          originalMessage: cleanMessage
        });

      } catch (error) {
        Utils.handleError(error, 'processBatchMessages', 'BATCH');
        errors.push({
          success: false,
          error: error.message,
          originalMessage: message
        });
      }
    }

    return {
      results,
      errors,
      processed: messages.length,
      successful: results.length,
      failed: errors.length
    };
  },
};

// 消息监听
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  try {
    // 处理批处理消息
    if (message.opt === "batch") {
      background.processBatchMessages(message.messages || [])
        .then(results => sendResponse({ success: true, results }))
        .catch(error => sendResponse({ success: false, error: error.message }));
      return true;
    }

    // 处理事件消息
    if (message.opt === "event") {
      switch (message.event) {
        case "domainRulesUpdated":
          background.handleDomainRulesUpdate(message.data);
          sendResponse({ success: true });
          break;
        case "translatorToolbarUpdated":
          // 处理划词工具栏设置更新
          console.log("划词工具栏设置已更新:", message.data);
          // 广播设置变更到所有标签页
          chrome.tabs.query({}, (tabs) => {
            tabs.forEach((tab) => {
              if (tab.url && tab.url.startsWith("http")) {
                chrome.tabs
                  .sendMessage(tab.id, {
                    opt: "event",
                    event: "translatorToolbarSettingChanged",
                    data: message.data,
                  })
                  .catch(() => {
                    // 忽略发送失败的错误
                  });
              }
            });
          });
          sendResponse({ success: true });
          break;
        default:
          console.warn("未处理的事件类型:", message.event);
          sendResponse({ success: false, error: "未处理的事件类型" });
      }
      return true;
    }

    // 处理 RPC 消息
    if (message.opt === "rpc") {
      // 验证消息格式
      if (!background._verifyMessage(message)) {
        sendResponse({ success: false, error: "无效的RPC消息格式" });
        return true;
      }

      // 优先处理优化函数
      if (message.func === "setUrlActiveStatusOptimized") {
        background.setUrlActiveStatusOptimized
          .apply(background, message.args || [])
          .then((result) => sendResponse({ success: true, result }))
          .catch((error) =>
            sendResponse({ success: false, error: error.message })
          );
        return true;
      }

      // 批量处理多个函数调用
      if (message.func === "batch") {
        background.processBatchMessages(message.args).then(sendResponse);
        return true;
      }

      // 调用对应的函数
      const func = background[message.func];
      if (typeof func === "function") {
        func.apply(background, message.args || []).then(sendResponse);
        return true;
      } else {
        sendResponse({ success: false, error: "函数不存在" });
      }
      return true;
    }
  } catch (error) {
    console.error("处理消息时出错:", error);
    sendResponse({ success: false, error: error.message });
  }
  return true;
});

// 标签页事件处理
chrome.tabs.onRemoved.addListener((tabId) => {
  // 清理总开关状态
  background.tabActiveStatus.delete(tabId);
  chrome.storage.local.remove(`${background.TAB_ACTIVE_STATUS}_${tabId}`);

  // 清理分类开关状态
  // 为避免过多的存储操作，使用批量删除
  const keysToRemove = [];

  // 清理内存中的键
  background.tabCategoryStatus.forEach((value, key) => {
    if (key.startsWith(`${tabId}_`)) {
      background.tabCategoryStatus.delete(key);
    }
  });

  // 获取存储中以该标签页ID开头的所有键
  const categoryKeyPrefix = `${background.TAB_CATEGORY_STATUS}_${tabId}_`;
  chrome.storage.local.get(null, (items) => {
    Object.keys(items).forEach((key) => {
      if (key.startsWith(categoryKeyPrefix)) {
        keysToRemove.push(key);
      }
    });

    // 一次性批量删除存储
    if (keysToRemove.length > 0) {
      chrome.storage.local.remove(keysToRemove);
      console.log(`已清理标签页 ${tabId} 的 ${keysToRemove.length} 个分类状态`);
    }
  });
});

// 注册右键菜单和初始化插件
chrome.runtime.onInstalled.addListener(async () => {
  try {
    // 先获取当前状态,使用正确的键名
    const currentStatus = await chrome.storage.local.get([
      background.ACTIVE_STATUS_STORE,
    ]);

    // 只设置插件状态为启用,不创建默认分类
    if (!currentStatus[background.ACTIVE_STATUS_STORE]) {
      await chrome.storage.local.set({
        [background.ACTIVE_STATUS_STORE]: "true", // 使用字符串"true"保持一致性
      });
    }

    // 创建右键菜单
    chrome.contextMenus.create({
      id: "add-to-category",
      title: "添加到高亮分类",
      contexts: ["selection"],
    });

    chrome.contextMenus.create({
      id: "remove-highlight",
      title: "删除高亮",
      contexts: ["selection"],
    });
  } catch (error) {
    console.error("初始化插件失败:", error);
  }
});

// 处理右键菜单点击
chrome.contextMenus.onClicked.addListener((info, tab) => {
  if (info.menuItemId === "add-to-category") {
    chrome.tabs
      .sendMessage(tab.id, {
        type: "add-to-category",
      })
      .catch(() => {});
  } else if (info.menuItemId === "remove-highlight") {
    chrome.tabs
      .sendMessage(tab.id, {
        type: "remove-highlight",
      })
      .catch(() => {});
  }
});

// 处理快捷键
chrome.commands.onCommand.addListener((command, tab) => {
  if (command === "add-to-category") {
    chrome.tabs
      .sendMessage(tab.id, {
        type: "add-to-category",
      })
      .catch(() => {});
  } else if (command === "remove-highlight") {
    chrome.tabs
      .sendMessage(tab.id, {
        type: "remove-highlight",
      })
      .catch(() => {});
  }
});

// 检查是否启用文本工具
chrome.tabs.onUpdated.addListener(function (tabId, changeInfo, tab) {
  if (changeInfo.status === "complete" && tab.url && tab.url.match(/^http/)) {
    // 检查是否启用划词工具栏
    chrome.storage.sync.get(
      background.TRANSLATOR_TOOLBAR_KEY,
      function (result) {
        // 只有当设置为true或未设置（默认为true）时，才注入脚本
        if (result[background.TRANSLATOR_TOOLBAR_KEY] !== false) {
          chrome.scripting.executeScript({
            target: { tabId: tabId },
            files: ["js/translator.js"],
          });
        }
      }
    );
  }
});

async function setupContextMenu() {
  try {
    // 移除所有现有菜单
    await chrome.contextMenus.removeAll();

    // 添加选项页面菜单
    chrome.contextMenus.create({
      id: "options",
      title: "网站高亮设置",
      contexts: ["action"],
    });
  } catch (error) {
    console.error("设置上下文菜单失败:", error);
  }
}

// 添加 - 监听tab更新事件
chrome.tabs.onUpdated.addListener(async (tabId, changeInfo, tab) => {
  if (changeInfo.status === "complete" && tab.url) {
    // 重要：在标签页完成加载时更新应用的规则缓存
    await background.updateAppliedRules();

    // 检查并更新高亮状态
    await background.updateTabHighlightStatus(tabId, tab.url);
  }
});

// 添加 - 监听tab激活事件
chrome.tabs.onActivated.addListener(async (activeInfo) => {
  try {
    const tab = await chrome.tabs.get(activeInfo.tabId);
    if (tab.url) {
      // 注意：标签激活时不更新规则缓存，只在页面刷新时更新
      // 检查并更新高亮状态
      await background.updateTabHighlightStatus(tab.id, tab.url);
    }
  } catch (error) {
    console.error("标签激活处理失败:", error);
  }
});

// 添加 - 处理右键菜单点击
chrome.contextMenus.onClicked.addListener((info, tab) => {
  if (info.menuItemId === "options") {
    chrome.runtime.openOptionsPage();
  }
});

// 添加 - 初始化扩展
async function initializeExtension() {
  // 设置右键菜单
  await setupContextMenu();

  // 加载域名规则
  await background.getDomainRules();
}

// 扩展启动时初始化
chrome.runtime.onInstalled.addListener(initializeExtension);
